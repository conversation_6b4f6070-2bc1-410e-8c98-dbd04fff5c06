# 达梦数据库到PostgreSQL迁移指南

## 迁移概述

本项目已从达梦数据库成功迁移到PostgreSQL。以下是已完成的迁移步骤和后续需要执行的操作。

## 已完成的迁移步骤

### 1. ✅ 更新依赖包
- **移除**: `dmPython==2.5.5`, `dmSQLAlchemy==2.0.2`
- **添加**: `psycopg2-binary==2.9.9`
- **文件**: `requirements.txt`

### 2. ✅ 更新数据库配置
- **文件**: `app/config.py`
- **修改**: 数据库连接字符串从达梦格式改为PostgreSQL格式
- **原配置**: `dm+dmPython://user_center:user_center123456@**********:5236/`
- **新配置**: `postgresql://user_center:user_center123456@localhost:5432/user_center`

### 3. ✅ 更新迁移配置
- **文件**: `migrations/env.py`
  - 移除达梦方言导入: `from sqlalchemy_dm import dialect`
  - 移除方言名称配置: `dialect_name="dm"`
- **文件**: `migrations/alembic.ini`
  - 更新连接字符串为PostgreSQL格式

### 4. ✅ 移除达梦特定代码
- **删除文件**: `docker/dmPython.py`
- **更新文件**: `docker/BaseDockerfile` (移除达梦相关注释)

### 5. ✅ 数据类型兼容性检查
- 所有使用的SQLAlchemy数据类型都与PostgreSQL兼容
- 主要类型: `TIMESTAMP`, `String`, `Integer`, `SmallInteger`, `Boolean`, `Text`, `Date`, `DateTime`

## 需要手动执行的步骤

### 1. 安装PostgreSQL依赖
```bash
pip install psycopg2-binary==2.9.9
```

### 2. 设置PostgreSQL数据库
确保PostgreSQL服务正在运行，并创建相应的数据库和用户：

```sql
-- 创建数据库
CREATE DATABASE user_center;

-- 创建用户（如果不存在）
CREATE USER user_center WITH PASSWORD 'user_center123456';

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE user_center TO user_center;
```

### 3. 配置数据库连接
根据您的PostgreSQL实际配置，更新以下文件中的连接参数：

**app/config.py**:
```python
SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URI', 'postgresql://用户名:密码@主机:端口/数据库名')
```

**migrations/alembic.ini**:
```ini
sqlalchemy.url = postgresql://用户名:密码@主机:端口/数据库名
```

### 4. 测试数据库连接
运行提供的测试脚本：
```bash
python test_postgresql_connection.py
```

### 5. 数据迁移
如果需要从达梦数据库迁移现有数据到PostgreSQL：

1. **导出达梦数据库数据**:
   ```bash
   # 使用达梦数据库工具导出数据为SQL格式
   ```

2. **转换SQL语法**:
   - 将达梦特有的SQL语法转换为PostgreSQL兼容的语法
   - 注意数据类型差异和函数名称差异

3. **导入到PostgreSQL**:
   ```bash
   psql -U user_center -d user_center -f exported_data.sql
   ```

### 6. 重新生成数据库迁移
```bash
# 删除现有迁移文件（如果需要）
rm -rf migrations/versions/*

# 生成新的初始迁移
flask db init  # 如果migrations目录不存在
flask db migrate -m "Initial migration for PostgreSQL"
flask db upgrade
```

## 环境变量配置

建议使用环境变量来配置数据库连接：

```bash
export DATABASE_URI="postgresql://user_center:user_center123456@localhost:5432/user_center"
```

或在`.env`文件中：
```
DATABASE_URI=postgresql://user_center:user_center123456@localhost:5432/user_center
```

## 验证迁移成功

1. **启动应用**:
   ```bash
   python run.py
   ```

2. **检查日志**: 确保没有数据库连接错误

3. **测试API**: 验证所有功能正常工作

4. **检查数据**: 确保数据正确迁移（如果适用）

## 注意事项

1. **备份**: 在执行迁移前，请确保备份原达梦数据库的数据
2. **测试**: 在生产环境部署前，请在测试环境充分测试
3. **性能**: PostgreSQL的查询优化可能与达梦不同，可能需要调整索引和查询
4. **字符集**: 确保PostgreSQL使用UTF-8字符集以支持中文
5. **时区**: 注意时区设置，确保时间数据的一致性

## 故障排除

### 常见问题

1. **连接失败**: 检查PostgreSQL服务状态和连接参数
2. **权限错误**: 确保数据库用户有足够的权限
3. **编码问题**: 确保数据库和客户端都使用UTF-8编码
4. **端口冲突**: 确认PostgreSQL端口（默认5432）没有被占用

### 日志检查
```bash
# 检查PostgreSQL日志
tail -f /var/log/postgresql/postgresql-*.log

# 检查应用日志
tail -f logs/app.log
```

## 完成状态

- [x] 依赖包更新
- [x] 数据库配置更新  
- [x] 迁移配置更新
- [x] 达梦特定代码移除
- [x] 数据类型兼容性检查
- [x] Docker配置更新
- [x] 测试脚本创建
- [ ] 实际数据库连接测试（需要PostgreSQL环境）
- [ ] 数据迁移（如需要）
- [ ] 生产环境部署测试
