# getAllTreeData 接口更新说明

## 更新内容

### 主要变更
将 `getAllTreeData` 接口从使用 `_parent` 字段改为使用 `parent_org_code` 字段来构建树形结构。

### 最新更新（无参数时的根节点处理）
当 `getAllTreeData` 接口没有传递 `parentCode` 参数时，现在会：
1. 获取所有符合条件的组织数据
2. 构建以 `parent_org_code=null` 的节点为根的完整树形结构
3. 返回完整的组织层级树

### 技术实现

#### 1. 新增方法
- `_build_tree_with_parent_org_code()`: 专门使用 `parent_org_code` 构建树结构
- `search_organizations_with_parent_org_code()`: 使用 `parent_org_code` 的搜索方法

#### 2. 字段差异
| 字段 | 说明 | 用途 |
|------|------|------|
| `_parent` | 外键，指向父组织的code | 数据库关系，用于查询优化 |
| `parent_org_code` | 父级机构编号 | 业务逻辑，用于构建组织层级 |

#### 3. 参数处理逻辑
- **有 parentCode**: 查询指定父节点下的子节点
- **无 parentCode**: 查询所有数据，构建以 `parent_org_code=null` 为根的完整树
- **as_tree=true**: 始终返回树形结构

#### 4. 更新的接口
- **路由**: `/api/organizations/getAllTreeData`
- **参数**: `parentCode` 现在对应 `parent_org_code` 字段
- **响应**: 树节点中的 `parent_org_code` 字段现在显示正确的父级机构编号

### 使用示例

#### 1. 获取完整树形结构（无参数）
```http
GET /api/organizations/getAllTreeData
```
返回以 `parent_org_code=null` 为根的完整组织树。

#### 2. 获取指定父节点的子树
```http
GET /api/organizations/getAllTreeData?parentCode=50001000&includeDepartments=true
```

#### 响应结构
```json
{
  "code": 200,
  "data": [
    {
      "code": "50001001",
      "name": "子机构名称",
      "type": "enterprise",
      "parent_org_code": "50001000",
      "level": "02",
      "children": [...]
    }
  ]
}
```

### 核心逻辑变更

#### search_organizations_with_parent_org_code 方法
```python
# 处理 parent_code 参数
parent_code = options.get('parent_code')
if parent_code is not None and parent_code != '':
    # 指定了 parent_code，只查询该父节点下的数据
    query = query.filter(cls.parent_org_code == parent_code)
elif options.get('as_tree', False):
    # 需要树形结构且没有指定 parent_code，获取所有数据来构建完整树
    pass  # 不添加过滤条件，获取所有符合条件的数据
else:
    # 不需要树形结构且没有指定 parent_code，只返回根节点
    query = query.filter(cls.parent_org_code.is_(None))
```

#### _build_tree_with_parent_org_code 方法
```python
if parent_code is not None and parent_code != '':
    # 如果指定了具体的 parent_code，构建该节点的子树
    return build_children(parent_code)

# 当 parent_code 为 None 或空字符串时，从根节点开始构建树
# 根节点是那些 parent_org_code 为 None 的节点
root_orgs = children_map.get(None, [])
```

### 向后兼容性
- 接口URL和参数名称保持不变
- 只是内部实现逻辑更改为使用 `parent_org_code`
- 响应结构保持兼容，只是数据来源字段变更
- 无参数调用现在返回完整树形结构，而不是空结果

### 优势
1. **业务逻辑清晰**: 使用明确的业务字段而非数据库关系字段
2. **数据一致性**: `parent_org_code` 是专门为组织层级设计的字段
3. **维护性**: 减少数据库字段和业务逻辑的耦合
4. **默认行为改进**: 无参数时返回完整树，提供更好的默认体验

### 测试建议
1. 验证无参数时返回的树形结构正确性
2. 确认根节点的 `parent_org_code` 为 `null`
3. 验证树形结构的完整性和父子关系
4. 测试不同 `parentCode` 参数的查询结果
5. 验证空值和边界情况的处理
6. 确认日志输出的调试信息准确性 