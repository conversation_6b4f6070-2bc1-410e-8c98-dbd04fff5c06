# Python 缓存文件
*.pyc
*.pyo
__pycache__/

# IDE 和编辑器相关文件
.vscode/
.idea/

# 虚拟环境相关
*.env
*.venv/
ENV/
env/
venv/

# 数据库文件
*.sqlite3
*.db
*.db-journal

# 日志文件
logs/
*.log

# 本地配置文件
*.local
*.config.json
*.secret

# Docker 文件
# *.dockerignore
docker-compose.override.yml

# DevContainer 文件
.devcontainer/

# 操作系统生成的文件
.DS_Store
Thumbs.db

# 静态文件或临时文件
static/
media/
tmp/
*.swp
*.bak

# Migrations 文件夹中自动生成的缓存文件
migrations/versions/*.pyc
migrations/versions/*.pyo
migrations/__pycache__/

# pip 缓存
pip-log.txt
pip-delete-this-directory.txt

# Flask-SQLAlchemy 本地数据库文件
app/local.db

# 避免的敏感文件
*.crt
*.pem
*.key

# Docker 文件
# Dockerfile
