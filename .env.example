# 数据库配置
DATABASE_URI=postgresql://username:password@localhost:5432/database_name
SQLALCHEMY_TRACK_MODIFICATIONS=False
SQLALCHEMY_ECHO=False

# 应用配置
SECRET_KEY=your-secret-key-change-in-production
ENV=development
DEBUG=True

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=./logs

# JWT配置
JWT_SECRET_KEY=jwt-secret-key-change-in-production
JWT_ACCESS_TOKEN_EXPIRES=86400

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 管理密码
MANAGE_PASSWORD=123456

# 竹云OAuth配置
ZY_OAUTH_HOST=https://iam-t.crsc.isc/idp
ZY_OAUTH_CLIENT_ID=AZSQ
ZY_OAUTH_CLIENT_SECRET=your-oauth-client-secret
ZY_OAUTH_RESPONSE_TYPE=code
ZY_OAUTH_GRANT_TYPE=authorization_code
ZY_OAUTH_REDIRECT_URI=
ZY_OAUTH_LOGOUT_URL=https://iam-t.crsc.isc/idp/profile/OAUTH2/Redirect/GLO

# 竹云数据同步配置
ZY_SYNC_DATA_API_URL=https://iam-t.crsc.isc/bim-server/integration/api.json
ZY_SYNC_DATA_SYSTEM_CODE=AZSQ
ZY_SYNC_DATA_INTEGRATION_KEY=your-integration-key
