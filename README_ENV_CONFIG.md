# 环境配置说明

## 概述

项目配置已从 `app/config.py` 中的硬编码值迁移到 `env.sh` 环境变量文件中，实现了配置与代码的分离，提高了安全性和灵活性。

## 配置文件结构

### 1. env.sh - 环境变量配置文件
包含所有应用配置项，使用 `export` 命令设置环境变量。

### 2. app/config.py - 配置类定义
从环境变量中读取配置值，不再包含硬编码的默认值。

## 配置项说明

### 数据库配置
```bash
# PostgreSQL数据库连接字符串
export DATABASE_URI='***************************************************/user_center'

# SQLAlchemy配置
export SQLALCHEMY_TRACK_MODIFICATIONS=False  # 禁用对象修改跟踪
export SQLALCHEMY_ECHO=True                  # 启用SQL查询日志
```

### 应用配置
```bash
# Flask应用密钥
export SECRET_KEY='your-secret-key'

# 运行环境
export ENV='development'

# 调试模式
export DEBUG=True
```

### 日志配置
```bash
# 日志级别
export LOG_LEVEL='DEBUG'

# 日志文件目录
export LOG_DIR='./logs'
```

### JWT认证配置
```bash
# JWT密钥
export JWT_SECRET_KEY='helloworld'

# JWT令牌过期时间（秒）
export JWT_ACCESS_TOKEN_EXPIRES=86400
```

### Redis配置
```bash
# Redis连接字符串
export REDIS_URL="redis://:Redis!23@************:6379/0"
```

### 系统管理配置
```bash
# 系统管理员密码（用于root和admin用户）
export MANAGE_PASSWORD='!helloworld$_321'
```

### 竹云OAuth配置
```bash
# OAuth服务器地址
export ZY_OAUTH_HOST='https://iam-t.crsc.isc/idp'

# OAuth客户端ID
export ZY_OAUTH_CLIENT_ID='AZSQ'

# OAuth客户端密钥
export ZY_OAUTH_CLIENT_SECRET='0f28d10195714d6ba511838ded07099c'

# OAuth响应类型
export ZY_OAUTH_RESPONSE_TYPE='code'

# OAuth授权类型
export ZY_OAUTH_GRANT_TYPE='authorization_code'

# OAuth重定向URI（可选）
export ZY_OAUTH_REDIRECT_URI=''

# OAuth登出URL
export ZY_OAUTH_LOGOUT_URL='https://iam-t.crsc.isc/idp/profile/OAUTH2/Redirect/GLO'
```

### 竹云数据同步配置
```bash
# 数据同步API地址
export ZY_SYNC_DATA_API_URL='https://iam-t.crsc.isc/bim-server/integration/api.json'

# 系统代码
export ZY_SYNC_DATA_SYSTEM_CODE='AZSQ'

# 集成密钥
export ZY_SYNC_DATA_INTEGRATION_KEY='Crsc@AZSQ'
```

## 使用方法

### 1. 启动应用
```bash
# 加载环境变量并启动应用
source env.sh && python run.py
```

### 2. 修改配置
直接编辑 `env.sh` 文件中的相应配置项，然后重新启动应用。

### 3. 生产环境部署
在生产环境中，建议：
- 将敏感配置（如密码、密钥）存储在安全的环境变量管理系统中
- 不要将 `env.sh` 文件提交到版本控制系统
- 为不同环境创建不同的配置文件

## 安全注意事项

1. **敏感信息保护**：
   - `env.sh` 文件包含敏感信息，不应提交到版本控制系统
   - 建议将 `env.sh` 添加到 `.gitignore` 文件中

2. **文件权限**：
   ```bash
   # 设置适当的文件权限
   chmod 600 env.sh
   ```

3. **生产环境**：
   - 使用更强的密码和密钥
   - 定期轮换敏感配置
   - 使用专业的密钥管理服务

## 系统用户信息

应用启动时会自动创建/更新系统用户：

- **root用户**：超级管理员
  - 用户名：`root`
  - 密码：`MANAGE_PASSWORD` 环境变量的值
  - 权限：`super`

- **admin用户**：管理员
  - 用户名：`admin`
  - 密码：`MANAGE_PASSWORD` 环境变量的值
  - 权限：`admin`

## 验证配置

启动应用后，可以通过以下方式验证配置是否正确：

1. **检查应用启动日志**：
   ```
   System users initialization completed successfully
   * Running on http://127.0.0.1:5000
   ```

2. **测试登录**：
   ```bash
   curl -X POST "http://127.0.0.1:5000/wolf/user/login" \
        -H "Content-Type: application/json" \
        -d '{"username": "root", "password": "!helloworld$_321"}'
   ```

3. **检查数据库连接**：
   应用启动时会显示PostgreSQL连接日志。

## 故障排除

1. **环境变量未生效**：
   - 确保使用 `source env.sh` 加载环境变量
   - 检查环境变量语法是否正确

2. **数据库连接失败**：
   - 验证 `DATABASE_URI` 配置
   - 确保PostgreSQL服务正在运行
   - 检查网络连接和防火墙设置

3. **Redis连接失败**：
   - 验证 `REDIS_URL` 配置
   - 确保Redis服务正在运行

4. **JWT认证失败**：
   - 检查 `JWT_SECRET_KEY` 配置
   - 验证token格式和header设置
