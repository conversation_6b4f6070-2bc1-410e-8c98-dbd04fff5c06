work-ubuntu-130
    description: Computer
    product: VMware Virtual Platform
    vendor: VMware, Inc.
    version: None
    serial: VMware-42 2d f2 e5 74 e6 27 ee-7c 70 cc 6b 7f e1 c3 74
    width: 64 bits
    capabilities: smbios-2.7 dmi-2.7 smp vsyscall32
    configuration: administrator_password=enabled boot=normal frontpanel_password=unknown keyboard_password=unknown power-on_password=disabled uuid=e5f22d42-e674-ee27-7c70-cc6b7fe1c374
  *-core
       description: Motherboard
       product: 440BX Desktop Reference Platform
       vendor: Intel Corporation
       physical id: 0
       version: None
       serial: None
     *-firmware
          description: BIOS
          vendor: Phoenix Technologies LTD
          physical id: 0
          version: 6.00
          date: 11/12/2020
          size: 86KiB
          capabilities: isa pci pcmcia pnp apm upgrade shadowing escd cdboot bootselect edd int5printscreen int9keyboard int14serial int17printer int10video acpi smartbattery biosbootspecification netboot
     *-cpu:0
          description: CPU
          product: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
          vendor: Intel Corp.
          physical id: 1
          bus info: cpu@0
          version: 6.62.4
          slot: CPU #000
          size: 2200MHz
          capacity: 4230MHz
          width: 64 bits
          capabilities: lm fpu fpu_exception wp vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss syscall nx rdtscp x86-64 constant_tsc arch_perfmon nopl xtopology tsc_reliable nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 cx16 pcid sse4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm cpuid_fault pti ssbd ibrs ibpb stibp fsgsbase tsc_adjust smep arat md_clear flush_l1d arch_capabilities
          configuration: cores=1 enabledcores=1 microcode=1070
        *-cache:0
             description: L1 cache
             physical id: 0
             slot: L1
             size: 16KiB
             capacity: 16KiB
             capabilities: asynchronous internal write-back
             configuration: level=1
        *-cache:1
             description: L1 cache
             physical id: 1
             slot: L1
             size: 16KiB
             capacity: 16KiB
             capabilities: asynchronous internal write-back
             configuration: level=1
     *-cpu:1
          description: CPU
          product: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
          vendor: Intel Corp.
          physical id: 2
          bus info: cpu@1
          version: 6.62.4
          slot: CPU #001
          size: 2200MHz
          capacity: 4230MHz
          width: 64 bits
          capabilities: lm fpu fpu_exception wp vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss syscall nx rdtscp x86-64 constant_tsc arch_perfmon nopl xtopology tsc_reliable nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 cx16 pcid sse4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm cpuid_fault pti ssbd ibrs ibpb stibp fsgsbase tsc_adjust smep arat md_clear flush_l1d arch_capabilities
          configuration: cores=1 enabledcores=1 microcode=1070
     *-cpu:2
          description: CPU
          product: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
          vendor: Intel Corp.
          physical id: 5
          bus info: cpu@2
          version: 6.62.4
          slot: CPU #002
          size: 2200MHz
          capacity: 4230MHz
          width: 64 bits
          capabilities: lm fpu fpu_exception wp vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss syscall nx rdtscp x86-64 constant_tsc arch_perfmon nopl xtopology tsc_reliable nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 cx16 pcid sse4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm cpuid_fault pti ssbd ibrs ibpb stibp fsgsbase tsc_adjust smep arat md_clear flush_l1d arch_capabilities
          configuration: cores=1 enabledcores=1 microcode=1070
        *-cache
             description: L1 cache
             physical id: 25
             slot: L1
             size: 16KiB
             capacity: 16KiB
             capabilities: asynchronous internal write-back
             configuration: level=1
           *-bank:0
                description: DIMM [empty]
                physical id: 0
                slot: NVD #0
                width: 32 bits
           *-bank:1
                description: DIMM [empty]
                physical id: 1
                slot: NVD #1
                width: 32 bits
           *-bank:2
                description: DIMM [empty]
                physical id: 2
                slot: NVD #2
                width: 32 bits
           *-bank:3
                description: DIMM [empty]
                physical id: 3
                slot: NVD #3
                width: 32 bits
           *-bank:4
                description: DIMM [empty]
                physical id: 4
                slot: NVD #4
                width: 32 bits
           *-bank:5
                description: DIMM [empty]
                physical id: 5
                slot: NVD #5
                width: 32 bits
           *-bank:6
                description: DIMM [empty]
                physical id: 6
                slot: NVD #6
                width: 32 bits
           *-bank:7
                description: DIMM [empty]
                physical id: 7
                slot: NVD #7
                width: 32 bits
           *-bank:8
                description: DIMM [empty]
                physical id: 8
                slot: NVD #8
                width: 32 bits
           *-bank:9
                description: DIMM [empty]
                physical id: 9
                slot: NVD #9
                width: 32 bits
           *-bank:10
                description: DIMM [empty]
                physical id: a
                slot: NVD #10
                width: 32 bits
           *-bank:11
                description: DIMM [empty]
                physical id: b
                slot: NVD #11
                width: 32 bits
           *-bank:12
                description: DIMM [empty]
                physical id: c
                slot: NVD #12
                width: 32 bits
           *-bank:13
                description: DIMM [empty]
                physical id: d
                slot: NVD #13
                width: 32 bits
           *-bank:14
                description: DIMM [empty]
                physical id: e
                slot: NVD #14
                width: 32 bits
           *-bank:15
                description: DIMM [empty]
                physical id: f
                slot: NVD #15
                width: 32 bits
           *-bank:16
                description: DIMM [empty]
                physical id: 10
                slot: NVD #16
                width: 32 bits
           *-bank:17
                description: DIMM [empty]
                physical id: 11
                slot: NVD #17
                width: 32 bits
           *-bank:18
                description: DIMM [empty]
                physical id: 12
                slot: NVD #18
                width: 32 bits
           *-bank:19
                description: DIMM [empty]
                physical id: 13
                slot: NVD #19
                width: 32 bits
           *-bank:20
                description: DIMM [empty]
                physical id: 14
                slot: NVD #20
                width: 32 bits
           *-bank:21
                description: DIMM [empty]
                physical id: 15
                slot: NVD #21
                width: 32 bits
           *-bank:22
                description: DIMM [empty]
                physical id: 16
                slot: NVD #22
                width: 32 bits
           *-bank:23
                description: DIMM [empty]
                physical id: 17
                slot: NVD #23
                width: 32 bits
           *-bank:24
                description: DIMM [empty]
                physical id: 18
                slot: NVD #24
                width: 32 bits
           *-bank:25
                description: DIMM [empty]
                physical id: 19
                slot: NVD #25
                width: 32 bits
           *-bank:26
                description: DIMM [empty]
                physical id: 1a
                slot: NVD #26
                width: 32 bits
           *-bank:27
                description: DIMM [empty]
                physical id: 1b
                slot: NVD #27
                width: 32 bits
           *-bank:28
                description: DIMM [empty]
                physical id: 1c
                slot: NVD #28
                width: 32 bits
           *-bank:29
                description: DIMM [empty]
                physical id: 1d
                slot: NVD #29
                width: 32 bits
           *-bank:30
                description: DIMM [empty]
                physical id: 1e
                slot: NVD #30
                width: 32 bits
           *-bank:31
                description: DIMM [empty]
                physical id: 1f
                slot: NVD #31
                width: 32 bits
           *-bank:32
                description: DIMM [empty]
                physical id: 20
                slot: NVD #32
                width: 32 bits
           *-bank:33
                description: DIMM [empty]
                physical id: 21
                slot: NVD #33
                width: 32 bits
           *-bank:34
                description: DIMM [empty]
                physical id: 22
                slot: NVD #34
                width: 32 bits
           *-bank:35
                description: DIMM [empty]
                physical id: 23
                slot: NVD #35
                width: 32 bits
           *-bank:36
                description: DIMM [empty]
                physical id: 24
                slot: NVD #36
                width: 32 bits
           *-bank:37
                description: DIMM [empty]
                physical id: 25
                slot: NVD #37
                width: 32 bits
           *-bank:38
                description: DIMM [empty]
                physical id: 26
                slot: NVD #38
                width: 32 bits
           *-bank:39
                description: DIMM [empty]
                physical id: 27
                slot: NVD #39
                width: 32 bits
           *-bank:40
                description: DIMM [empty]
                physical id: 28
                slot: NVD #40
                width: 32 bits
           *-bank:41
                description: DIMM [empty]
                physical id: 29
                slot: NVD #41
                width: 32 bits
           *-bank:42
                description: DIMM [empty]
                physical id: 2a
                slot: NVD #42
                width: 32 bits
           *-bank:43
                description: DIMM [empty]
                physical id: 2b
                slot: NVD #43
                width: 32 bits
           *-bank:44
                description: DIMM [empty]
                physical id: 2c
                slot: NVD #44
                width: 32 bits
           *-bank:45
                description: DIMM [empty]
                physical id: 2d
                slot: NVD #45
                width: 32 bits
           *-bank:46
                description: DIMM [empty]
                physical id: 2e
                slot: NVD #46
                width: 32 bits
           *-bank:47
                description: DIMM [empty]
                physical id: 2f
                slot: NVD #47
                width: 32 bits
           *-bank:48
                description: DIMM [empty]
                physical id: 30
                slot: NVD #48
                width: 32 bits
           *-bank:49
                description: DIMM [empty]
                physical id: 31
                slot: NVD #49
                width: 32 bits
           *-bank:50
                description: DIMM [empty]
                physical id: 32
                slot: NVD #50
                width: 32 bits
           *-bank:51
                description: DIMM [empty]
                physical id: 33
                slot: NVD #51
                width: 32 bits
           *-bank:52
                description: DIMM [empty]
                physical id: 34
                slot: NVD #52
                width: 32 bits
           *-bank:53
                description: DIMM [empty]
                physical id: 35
                slot: NVD #53
                width: 32 bits
           *-bank:54
                description: DIMM [empty]
                physical id: 36
                slot: NVD #54
                width: 32 bits
           *-bank:55
                description: DIMM [empty]
                physical id: 37
                slot: NVD #55
                width: 32 bits
           *-bank:56
                description: DIMM [empty]
                physical id: 38
                slot: NVD #56
                width: 32 bits
           *-bank:57
                description: DIMM [empty]
                physical id: 39
                slot: NVD #57
                width: 32 bits
           *-bank:58
                description: DIMM [empty]
                physical id: 3a
                slot: NVD #58
                width: 32 bits
           *-bank:59
                description: DIMM [empty]
                physical id: 3b
                slot: NVD #59
                width: 32 bits
           *-bank:60
                description: DIMM [empty]
                physical id: 3c
                slot: NVD #60
                width: 32 bits
           *-bank:61
                description: DIMM [empty]
                physical id: 3d
                slot: NVD #61
                width: 32 bits
           *-bank:62
                description: DIMM [empty]
                physical id: 3e
                slot: NVD #62
                width: 32 bits
           *-bank:63
                description: DIMM [empty]
                physical id: 3f
                slot: NVD #63
                width: 32 bits
     *-cpu:3
          description: CPU
          product: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
          vendor: Intel Corp.
          physical id: 6
          bus info: cpu@3
          version: 6.62.4
          slot: CPU #003
          size: 2200MHz
          capacity: 4230MHz
          width: 64 bits
          capabilities: lm fpu fpu_exception wp vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss syscall nx rdtscp x86-64 constant_tsc arch_perfmon nopl xtopology tsc_reliable nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 cx16 pcid sse4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm cpuid_fault pti ssbd ibrs ibpb stibp fsgsbase tsc_adjust smep arat md_clear flush_l1d arch_capabilities
          configuration: cores=1 enabledcores=1 microcode=1070
        *-cache
             description: L1 cache
             physical id: 26
             slot: L1
             size: 16KiB
             capacity: 16KiB
             capabilities: asynchronous internal write-back
             configuration: level=1
     *-cpu:4
          description: CPU
          product: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
          vendor: Intel Corp.
          physical id: 7
          bus info: cpu@4
          version: 6.62.4
          slot: CPU #004
          size: 2200MHz
          capacity: 4230MHz
          width: 64 bits
          capabilities: lm fpu fpu_exception wp vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss syscall nx rdtscp x86-64 constant_tsc arch_perfmon nopl xtopology tsc_reliable nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 cx16 pcid sse4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm cpuid_fault pti ssbd ibrs ibpb stibp fsgsbase tsc_adjust smep arat md_clear flush_l1d arch_capabilities
          configuration: cores=1 enabledcores=1 microcode=1070
        *-cache
             description: L1 cache
             physical id: 27
             slot: L1
             size: 16KiB
             capacity: 16KiB
             capabilities: asynchronous internal write-back
             configuration: level=1
     *-cpu:5
          description: CPU
          product: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
          vendor: Intel Corp.
          physical id: 8
          bus info: cpu@5
          version: 6.62.4
          slot: CPU #005
          size: 2200MHz
          capacity: 4230MHz
          width: 64 bits
          capabilities: lm fpu fpu_exception wp vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss syscall nx rdtscp x86-64 constant_tsc arch_perfmon nopl xtopology tsc_reliable nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 cx16 pcid sse4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm cpuid_fault pti ssbd ibrs ibpb stibp fsgsbase tsc_adjust smep arat md_clear flush_l1d arch_capabilities
          configuration: cores=1 enabledcores=1 microcode=1070
        *-cache
             description: L1 cache
             physical id: 28
             slot: L1
             size: 16KiB
             capacity: 16KiB
             capabilities: asynchronous internal write-back
             configuration: level=1
     *-cpu:6
          description: CPU
          product: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
          vendor: Intel Corp.
          physical id: 9
          bus info: cpu@6
          version: 6.62.4
          slot: CPU #006
          size: 2200MHz
          capacity: 4230MHz
          width: 64 bits
          capabilities: lm fpu fpu_exception wp vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss syscall nx rdtscp x86-64 constant_tsc arch_perfmon nopl xtopology tsc_reliable nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 cx16 pcid sse4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm cpuid_fault pti ssbd ibrs ibpb stibp fsgsbase tsc_adjust smep arat md_clear flush_l1d arch_capabilities
          configuration: cores=1 enabledcores=1 microcode=1070
        *-cache
             description: L1 cache
             physical id: 29
             slot: L1
             size: 16KiB
             capacity: 16KiB
             capabilities: asynchronous internal write-back
             configuration: level=1
     *-cpu:7
          description: CPU
          product: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
          vendor: Intel Corp.
          physical id: a
          bus info: cpu@7
          version: 6.62.4
          slot: CPU #007
          size: 2200MHz
          capacity: 4230MHz
          width: 64 bits
          capabilities: lm fpu fpu_exception wp vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss syscall nx rdtscp x86-64 constant_tsc arch_perfmon nopl xtopology tsc_reliable nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 cx16 pcid sse4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm cpuid_fault pti ssbd ibrs ibpb stibp fsgsbase tsc_adjust smep arat md_clear flush_l1d arch_capabilities
          configuration: cores=1 enabledcores=1 microcode=1070
        *-cache
             description: L1 cache
             physical id: 2a
             slot: L1
             size: 16KiB
             capacity: 16KiB
             capabilities: asynchronous internal write-back
             configuration: level=1
     *-cpu:8
          description: CPU
          product: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
          vendor: Intel Corp.
          physical id: b
          bus info: cpu@8
          version: 6.62.4
          slot: CPU #008
          size: 2200MHz
          capacity: 4230MHz
          width: 64 bits
          capabilities: lm fpu fpu_exception wp vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss syscall nx rdtscp x86-64 constant_tsc arch_perfmon nopl xtopology tsc_reliable nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 cx16 pcid sse4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm cpuid_fault pti ssbd ibrs ibpb stibp fsgsbase tsc_adjust smep arat md_clear flush_l1d arch_capabilities
          configuration: cores=1 enabledcores=1 microcode=1070
        *-cache
             description: L1 cache
             physical id: 2b
             slot: L1
             size: 16KiB
             capacity: 16KiB
             capabilities: asynchronous internal write-back
             configuration: level=1
     *-cpu:9
          description: CPU
          product: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
          vendor: Intel Corp.
          physical id: c
          bus info: cpu@9
          version: 6.62.4
          slot: CPU #009
          size: 2200MHz
          capacity: 4230MHz
          width: 64 bits
          capabilities: lm fpu fpu_exception wp vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss syscall nx rdtscp x86-64 constant_tsc arch_perfmon nopl xtopology tsc_reliable nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 cx16 pcid sse4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm cpuid_fault pti ssbd ibrs ibpb stibp fsgsbase tsc_adjust smep arat md_clear flush_l1d arch_capabilities
          configuration: cores=1 enabledcores=1 microcode=1070
        *-cache
             description: L1 cache
             physical id: 2c
             slot: L1
             size: 16KiB
             capacity: 16KiB
             capabilities: asynchronous internal write-back
             configuration: level=1
     *-cpu:10
          description: CPU
          product: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
          vendor: Intel Corp.
          physical id: d
          bus info: cpu@10
          version: 6.62.4
          slot: CPU #010
          size: 2200MHz
          capacity: 4230MHz
          width: 64 bits
          capabilities: lm fpu fpu_exception wp vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss syscall nx rdtscp x86-64 constant_tsc arch_perfmon nopl xtopology tsc_reliable nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 cx16 pcid sse4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm cpuid_fault pti ssbd ibrs ibpb stibp fsgsbase tsc_adjust smep arat md_clear flush_l1d arch_capabilities
          configuration: cores=1 enabledcores=1 microcode=1070
        *-cache
             description: L1 cache
             physical id: 2d
             slot: L1
             size: 16KiB
             capacity: 16KiB
             capabilities: asynchronous internal write-back
             configuration: level=1
     *-cpu:11
          description: CPU
          product: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
          vendor: Intel Corp.
          physical id: e
          bus info: cpu@11
          version: 6.62.4
          slot: CPU #011
          size: 2200MHz
          capacity: 4230MHz
          width: 64 bits
          capabilities: lm fpu fpu_exception wp vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss syscall nx rdtscp x86-64 constant_tsc arch_perfmon nopl xtopology tsc_reliable nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 cx16 pcid sse4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm cpuid_fault pti ssbd ibrs ibpb stibp fsgsbase tsc_adjust smep arat md_clear flush_l1d arch_capabilities
          configuration: cores=1 enabledcores=1 microcode=1070
        *-cache
             description: L1 cache
             physical id: 2e
             slot: L1
             size: 16KiB
             capacity: 16KiB
             capabilities: asynchronous internal write-back
             configuration: level=1
     *-cpu:12
          description: CPU
          product: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
          vendor: Intel Corp.
          physical id: f
          bus info: cpu@12
          version: 6.62.4
          slot: CPU #012
          size: 2200MHz
          capacity: 4230MHz
          width: 64 bits
          capabilities: lm fpu fpu_exception wp vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss syscall nx rdtscp x86-64 constant_tsc arch_perfmon nopl xtopology tsc_reliable nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 cx16 pcid sse4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm cpuid_fault pti ssbd ibrs ibpb stibp fsgsbase tsc_adjust smep arat md_clear flush_l1d arch_capabilities
          configuration: cores=1 enabledcores=1 microcode=1070
        *-cache
             description: L1 cache
             physical id: 2f
             slot: L1
             size: 16KiB
             capacity: 16KiB
             capabilities: asynchronous internal write-back
             configuration: level=1
     *-cpu:13
          description: CPU
          product: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
          vendor: Intel Corp.
          physical id: 10
          bus info: cpu@13
          version: 6.62.4
          slot: CPU #013
          size: 2200MHz
          capacity: 4230MHz
          width: 64 bits
          capabilities: lm fpu fpu_exception wp vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss syscall nx rdtscp x86-64 constant_tsc arch_perfmon nopl xtopology tsc_reliable nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 cx16 pcid sse4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm cpuid_fault pti ssbd ibrs ibpb stibp fsgsbase tsc_adjust smep arat md_clear flush_l1d arch_capabilities
          configuration: cores=1 enabledcores=1 microcode=1070
        *-cache
             description: L1 cache
             physical id: 30
             slot: L1
             size: 16KiB
             capacity: 16KiB
             capabilities: asynchronous internal write-back
             configuration: level=1
     *-cpu:14
          description: CPU
          product: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
          vendor: Intel Corp.
          physical id: 11
          bus info: cpu@14
          version: 6.62.4
          slot: CPU #014
          size: 2200MHz
          capacity: 4230MHz
          width: 64 bits
          capabilities: lm fpu fpu_exception wp vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss syscall nx rdtscp x86-64 constant_tsc arch_perfmon nopl xtopology tsc_reliable nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 cx16 pcid sse4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm cpuid_fault pti ssbd ibrs ibpb stibp fsgsbase tsc_adjust smep arat md_clear flush_l1d arch_capabilities
          configuration: cores=1 enabledcores=1 microcode=1070
        *-cache
             description: L1 cache
             physical id: 31
             slot: L1
             size: 16KiB
             capacity: 16KiB
             capabilities: asynchronous internal write-back
             configuration: level=1
     *-cpu:15
          description: CPU
          product: Intel(R) Xeon(R) CPU E5-2660 v2 @ 2.20GHz
          vendor: Intel Corp.
          physical id: 12
          bus info: cpu@15
          version: 6.62.4
          slot: CPU #015
          size: 2200MHz
          capacity: 4230MHz
          width: 64 bits
          capabilities: lm fpu fpu_exception wp vme de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ss syscall nx rdtscp x86-64 constant_tsc arch_perfmon nopl xtopology tsc_reliable nonstop_tsc cpuid tsc_known_freq pni pclmulqdq ssse3 cx16 pcid sse4_1 sse4_2 x2apic popcnt tsc_deadline_timer aes xsave avx f16c rdrand hypervisor lahf_lm cpuid_fault pti ssbd ibrs ibpb stibp fsgsbase tsc_adjust smep arat md_clear flush_l1d arch_capabilities
          configuration: cores=1 enabledcores=1 microcode=1070
        *-cache
             description: L1 cache
             physical id: 32
             slot: L1
             size: 16KiB
             capacity: 16KiB
             capabilities: asynchronous internal write-back
             configuration: level=1
     *-memory
          description: System Memory
          physical id: 52
          slot: System board or motherboard
          size: 26GiB
        *-bank:0
             description: DIMM DRAM EDO
             physical id: 0
             slot: RAM slot #0
             size: 16GiB
             width: 32 bits
        *-bank:1
             description: DIMM DRAM EDO
             physical id: 1
             slot: RAM slot #1
             size: 8GiB
             width: 32 bits
        *-bank:2
             description: DIMM DRAM EDO
             physical id: 2
             slot: RAM slot #2
             size: 2GiB
             width: 32 bits
        *-bank:3
             description: DIMM DRAM [empty]
             physical id: 3
             slot: RAM slot #3
        *-bank:4
             description: DIMM DRAM [empty]
             physical id: 4
             slot: RAM slot #4
        *-bank:5
             description: DIMM DRAM [empty]
             physical id: 5
             slot: RAM slot #5
        *-bank:6
             description: DIMM DRAM [empty]
             physical id: 6
             slot: RAM slot #6
        *-bank:7
             description: DIMM DRAM [empty]
             physical id: 7
             slot: RAM slot #7
        *-bank:8
             description: DIMM DRAM [empty]
             physical id: 8
             slot: RAM slot #8
        *-bank:9
             description: DIMM DRAM [empty]
             physical id: 9
             slot: RAM slot #9
        *-bank:10
             description: DIMM DRAM [empty]
             physical id: a
             slot: RAM slot #10
        *-bank:11
             description: DIMM DRAM [empty]
             physical id: b
             slot: RAM slot #11
        *-bank:12
             description: DIMM DRAM [empty]
             physical id: c
             slot: RAM slot #12
        *-bank:13
             description: DIMM DRAM [empty]
             physical id: d
             slot: RAM slot #13
        *-bank:14
             description: DIMM DRAM [empty]
             physical id: e
             slot: RAM slot #14
        *-bank:15
             description: DIMM DRAM [empty]
             physical id: f
             slot: RAM slot #15
        *-bank:16
             description: DIMM DRAM [empty]
             physical id: 10
             slot: RAM slot #16
        *-bank:17
             description: DIMM DRAM [empty]
             physical id: 11
             slot: RAM slot #17
        *-bank:18
             description: DIMM DRAM [empty]
             physical id: 12
             slot: RAM slot #18
        *-bank:19
             description: DIMM DRAM [empty]
             physical id: 13
             slot: RAM slot #19
        *-bank:20
             description: DIMM DRAM [empty]
             physical id: 14
             slot: RAM slot #20
        *-bank:21
             description: DIMM DRAM [empty]
             physical id: 15
             slot: RAM slot #21
        *-bank:22
             description: DIMM DRAM [empty]
             physical id: 16
             slot: RAM slot #22
        *-bank:23
             description: DIMM DRAM [empty]
             physical id: 17
             slot: RAM slot #23
        *-bank:24
             description: DIMM DRAM [empty]
             physical id: 18
             slot: RAM slot #24
        *-bank:25
             description: DIMM DRAM [empty]
             physical id: 19
             slot: RAM slot #25
        *-bank:26
             description: DIMM DRAM [empty]
             physical id: 1a
             slot: RAM slot #26
        *-bank:27
             description: DIMM DRAM [empty]
             physical id: 1b
             slot: RAM slot #27
        *-bank:28
             description: DIMM DRAM [empty]
             physical id: 1c
             slot: RAM slot #28
        *-bank:29
             description: DIMM DRAM [empty]
             physical id: 1d
             slot: RAM slot #29
        *-bank:30
             description: DIMM DRAM [empty]
             physical id: 1e
             slot: RAM slot #30
        *-bank:31
             description: DIMM DRAM [empty]
             physical id: 1f
             slot: RAM slot #31
        *-bank:32
             description: DIMM DRAM [empty]
             physical id: 20
             slot: RAM slot #32
        *-bank:33
             description: DIMM DRAM [empty]
             physical id: 21
             slot: RAM slot #33
        *-bank:34
             description: DIMM DRAM [empty]
             physical id: 22
             slot: RAM slot #34
        *-bank:35
             description: DIMM DRAM [empty]
             physical id: 23
             slot: RAM slot #35
        *-bank:36
             description: DIMM DRAM [empty]
             physical id: 24
             slot: RAM slot #36
        *-bank:37
             description: DIMM DRAM [empty]
             physical id: 25
             slot: RAM slot #37
        *-bank:38
             description: DIMM DRAM [empty]
             physical id: 26
             slot: RAM slot #38
        *-bank:39
             description: DIMM DRAM [empty]
             physical id: 27
             slot: RAM slot #39
        *-bank:40
             description: DIMM DRAM [empty]
             physical id: 28
             slot: RAM slot #40
        *-bank:41
             description: DIMM DRAM [empty]
             physical id: 29
             slot: RAM slot #41
        *-bank:42
             description: DIMM DRAM [empty]
             physical id: 2a
             slot: RAM slot #42
        *-bank:43
             description: DIMM DRAM [empty]
             physical id: 2b
             slot: RAM slot #43
        *-bank:44
             description: DIMM DRAM [empty]
             physical id: 2c
             slot: RAM slot #44
        *-bank:45
             description: DIMM DRAM [empty]
             physical id: 2d
             slot: RAM slot #45
        *-bank:46
             description: DIMM DRAM [empty]
             physical id: 2e
             slot: RAM slot #46
        *-bank:47
             description: DIMM DRAM [empty]
             physical id: 2f
             slot: RAM slot #47
        *-bank:48
             description: DIMM DRAM [empty]
             physical id: 30
             slot: RAM slot #48
        *-bank:49
             description: DIMM DRAM [empty]
             physical id: 31
             slot: RAM slot #49
        *-bank:50
             description: DIMM DRAM [empty]
             physical id: 32
             slot: RAM slot #50
        *-bank:51
             description: DIMM DRAM [empty]
             physical id: 33
             slot: RAM slot #51
        *-bank:52
             description: DIMM DRAM [empty]
             physical id: 34
             slot: RAM slot #52
        *-bank:53
             description: DIMM DRAM [empty]
             physical id: 35
             slot: RAM slot #53
        *-bank:54
             description: DIMM DRAM [empty]
             physical id: 36
             slot: RAM slot #54
        *-bank:55
             description: DIMM DRAM [empty]
             physical id: 37
             slot: RAM slot #55
        *-bank:56
             description: DIMM DRAM [empty]
             physical id: 38
             slot: RAM slot #56
        *-bank:57
             description: DIMM DRAM [empty]
             physical id: 39
             slot: RAM slot #57
        *-bank:58
             description: DIMM DRAM [empty]
             physical id: 3a
             slot: RAM slot #58
        *-bank:59
             description: DIMM DRAM [empty]
             physical id: 3b
             slot: RAM slot #59
        *-bank:60
             description: DIMM DRAM [empty]
             physical id: 3c
             slot: RAM slot #60
        *-bank:61
             description: DIMM DRAM [empty]
             physical id: 3d
             slot: RAM slot #61
        *-bank:62
             description: DIMM DRAM [empty]
             physical id: 3e
             slot: RAM slot #62
        *-bank:63
             description: DIMM DRAM [empty]
             physical id: 3f
             slot: RAM slot #63
     *-pci
          description: Host bridge
          product: 440BX/ZX/DX - 82443BX/ZX/DX Host bridge
          vendor: Intel Corporation
          physical id: 100
          bus info: pci@0000:00:00.0
          version: 01
          width: 32 bits
          clock: 33MHz
          configuration: driver=agpgart-intel
          resources: irq:0
        *-pci:0
             description: PCI bridge
             product: 440BX/ZX/DX - 82443BX/ZX/DX AGP bridge
             vendor: Intel Corporation
             physical id: 1
             bus info: pci@0000:00:01.0
             version: 01
             width: 32 bits
             clock: 66MHz
             capabilities: pci normal_decode bus_master
        *-isa
             description: ISA bridge
             product: 82371AB/EB/MB PIIX4 ISA
             vendor: Intel Corporation
             physical id: 7
             bus info: pci@0000:00:07.0
             version: 08
             width: 32 bits
             clock: 33MHz
             capabilities: isa bus_master
             configuration: latency=0
           *-pnp00:00
                product: PnP device PNP0c02
                physical id: 0
                capabilities: pnp
                configuration: driver=system
           *-pnp00:01
                product: PnP device PNP0b00
                physical id: 1
                capabilities: pnp
                configuration: driver=rtc_cmos
           *-pnp00:02
                product: PnP device PNP0303
                physical id: 2
                capabilities: pnp
                configuration: driver=i8042 kbd
           *-pnp00:03
                product: PnP device VMW0003
                physical id: 3
                capabilities: pnp
                configuration: driver=i8042 aux
           *-pnp00:04
                product: PnP device PNP0103
                physical id: 4
                capabilities: pnp
                configuration: driver=system
           *-pnp00:05
                product: PnP device PNP0c02
                physical id: 5
                capabilities: pnp
                configuration: driver=system
        *-ide
             description: IDE interface
             product: 82371AB/EB/MB PIIX4 IDE
             vendor: Intel Corporation
             physical id: 7.1
             bus info: pci@0000:00:07.1
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: ide isa_compat_mode pci_native_mode bus_master
             configuration: driver=ata_piix latency=64
             resources: irq:0 ioport:1f0(size=8) ioport:3f6 ioport:170(size=8) ioport:376 ioport:1060(size=16)
        *-bridge UNCLAIMED
             description: Bridge
             product: 82371AB/EB/MB PIIX4 ACPI
             vendor: Intel Corporation
             physical id: 7.3
             bus info: pci@0000:00:07.3
             version: 08
             width: 32 bits
             clock: 33MHz
             capabilities: bridge
             configuration: latency=0
        *-generic
             description: System peripheral
             product: Virtual Machine Communication Interface
             vendor: VMware
             physical id: 7.7
             bus info: pci@0000:00:07.7
             version: 10
             width: 64 bits
             clock: 33MHz
             capabilities: msi msix bus_master cap_list
             configuration: driver=vmw_vmci latency=64 maxlatency=255 mingnt=6
             resources: irq:16 ioport:1080(size=64) memory:febfe000-febfffff
        *-display
             description: VGA compatible controller
             product: SVGA II Adapter
             vendor: VMware
             physical id: f
             bus info: pci@0000:00:0f.0
             logical name: /dev/fb0
             version: 00
             width: 32 bits
             clock: 33MHz
             capabilities: vga_controller bus_master cap_list rom fb
             configuration: depth=32 driver=vmwgfx latency=64 resolution=1176,885
             resources: irq:16 ioport:1070(size=16) memory:e8000000-efffffff memory:fe000000-fe7fffff memory:c0000-dffff
        *-scsi
             description: SCSI storage controller
             product: 53c1030 PCI-X Fusion-MPT Dual Ultra320 SCSI
             vendor: Broadcom / LSI
             physical id: 10
             bus info: pci@0000:00:10.0
             logical name: scsi32
             version: 01
             width: 64 bits
             clock: 33MHz
             capabilities: scsi bus_master cap_list rom scsi-host
             configuration: driver=mptspi latency=64 maxlatency=255 mingnt=6
             resources: irq:17 ioport:1400(size=256) memory:feba0000-febbffff memory:febc0000-febdffff memory:c0200000-c0203fff
           *-disk:0
                description: SCSI Disk
                product: Virtual disk
                vendor: VMware
                physical id: 0.0.0
                bus info: scsi@32:0.0.0
                logical name: /dev/sda
                version: 2.0
                size: 160GiB (171GB)
                capabilities: gpt-1.00 partitioned partitioned:gpt
                configuration: ansiversion=6 guid=398dc208-8371-41ff-94bf-9af29dddb540 logicalsectorsize=512 sectorsize=512
              *-volume:0
                   description: BIOS Boot partition
                   vendor: EFI
                   physical id: 1
                   bus info: scsi@32:0.0.0,1
                   logical name: /dev/sda1
                   serial: 7047acc8-d96d-4032-8b6f-732cd5cf64b7
                   capacity: 1023KiB
                   capabilities: nofs
              *-volume:1
                   description: EXT4 volume
                   vendor: Linux
                   physical id: 2
                   bus info: scsi@32:0.0.0,2
                   logical name: /dev/sda2
                   logical name: /boot
                   version: 1.0
                   serial: cbffe283-256d-4f11-9825-3427c624c888
                   size: 2GiB
                   capabilities: journaled extended_attributes large_files huge_files dir_nlink recover 64bit extents ext4 ext2 initialized
                   configuration: created=2023-05-08 12:41:59 filesystem=ext4 lastmountpoint=/boot modified=2025-02-10 20:22:54 mount.fstype=ext4 mount.options=rw,relatime mounted=2025-02-10 20:22:54 state=mounted
              *-volume:2
                   description: EFI partition
                   physical id: 3
                   bus info: scsi@32:0.0.0,3
                   logical name: /dev/sda3
                   serial: R5sUyk-XdsC-uZko-OoN8-zzN1-Tesy-xg07CO
                   size: 157GiB
                   capabilities: lvm2
           *-disk:1
                description: SCSI Disk
                product: Virtual disk
                vendor: VMware
                physical id: 0.1.0
                bus info: scsi@32:0.1.0
                logical name: /dev/sdb
                version: 2.0
                size: 200GiB (214GB)
                capabilities: partitioned partitioned:dos
                configuration: ansiversion=6 logicalsectorsize=512 sectorsize=512 signature=0421ec07
              *-volume
                   description: EXT4 volume
                   vendor: Linux
                   physical id: 1
                   bus info: scsi@32:0.1.0,1
                   logical name: /dev/sdb1
                   logical name: /data
                   version: 1.0
                   serial: 3fc0c836-bb96-4e56-bfcc-00fb78c03789
                   size: 199GiB
                   capacity: 199GiB
                   capabilities: primary journaled extended_attributes large_files huge_files dir_nlink recover 64bit extents ext4 ext2 initialized
                   configuration: created=2023-05-24 08:02:07 filesystem=ext4 lastmountpoint=/data modified=2025-02-10 20:22:53 mount.fstype=ext4 mount.options=rw,relatime mounted=2025-02-10 20:22:53 state=mounted
           *-disk:2
                description: SCSI Disk
                product: Virtual disk
                vendor: VMware
                physical id: 0.2.0
                bus info: scsi@32:0.2.0
                logical name: /dev/sdc
                version: 2.0
                serial: uai0hS-Whae-3fUA-7bMF-IVPv-zlDG-G9U9Ek
                size: 100GiB
                capacity: 100GiB
                capabilities: lvm2
                configuration: ansiversion=6 logicalsectorsize=512 sectorsize=512
           *-disk:3
                description: SCSI Disk
                physical id: 0.3.0
                bus info: scsi@32:0.3.0
                logical name: /dev/sdd
                size: 120GiB (128GB)
                configuration: logicalsectorsize=512 sectorsize=512
        *-pci:1
             description: PCI bridge
             product: PCI bridge
             vendor: VMware
             physical id: 11
             bus info: pci@0000:00:11.0
             version: 02
             width: 32 bits
             clock: 33MHz
             capabilities: pci subtractive_decode bus_master cap_list
             resources: ioport:2000(size=8192) memory:fd500000-fdffffff ioport:e7b00000(size=5242880)
           *-sata
                description: SATA controller
                product: SATA AHCI controller
                vendor: VMware
                physical id: 0
                bus info: pci@0000:02:00.0
                logical name: scsi2
                version: 00
                width: 32 bits
                clock: 66MHz
                capabilities: sata pm msi ahci_1.0 bus_master cap_list rom emulated
                configuration: driver=ahci latency=64
                resources: irq:60 memory:fd5ff000-fd5fffff memory:fd500000-fd50ffff
              *-cdrom
                   description: DVD-RAM writer
                   product: VMware SATA CD00
                   vendor: NECVMWar
                   physical id: 0.0.0
                   bus info: scsi@2:0.0.0
                   logical name: /dev/cdrom
                   logical name: /dev/sr0
                   version: 1.00
                   capabilities: removable audio cd-r cd-rw dvd dvd-r dvd-ram
                   configuration: ansiversion=5 status=open
        *-pci:2
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 15
             bus info: pci@0000:00:15.0
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:24 ioport:4000(size=4096) memory:fd400000-fd4fffff ioport:c0000000(size=2097152)
           *-network
                description: Ethernet interface
                product: VMXNET3 Ethernet Controller
                vendor: VMware
                physical id: 0
                bus info: pci@0000:03:00.0
                logical name: ens160
                version: 01
                serial: 00:50:56:ad:0d:fe
                size: 10Gbit/s
                capacity: 10Gbit/s
                width: 32 bits
                clock: 33MHz
                capabilities: pm pciexpress msi msix bus_master cap_list rom ethernet physical logical tp 1000bt-fd 10000bt-fd
                configuration: autonegotiation=off broadcast=yes driver=vmxnet3 driverversion=*******-k-NAPI duplex=full ip=************** latency=0 link=yes multicast=yes port=twisted pair speed=10Gbit/s
                resources: irq:18 memory:fd4fc000-fd4fcfff memory:fd4fd000-fd4fdfff memory:fd4fe000-fd4fffff ioport:4000(size=16) memory:fd400000-fd40ffff
        *-pci:3
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 15.1
             bus info: pci@0000:00:15.1
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:25 ioport:8000(size=4096) memory:fd000000-fd0fffff ioport:e7700000(size=1048576)
        *-pci:4
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 15.2
             bus info: pci@0000:00:15.2
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:26 ioport:c000(size=4096) memory:fcc00000-fccfffff ioport:e7300000(size=1048576)
        *-pci:5
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 15.3
             bus info: pci@0000:00:15.3
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:27 memory:fc800000-fc8fffff ioport:e6f00000(size=1048576)
        *-pci:6
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 15.4
             bus info: pci@0000:00:15.4
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:28 memory:fc400000-fc4fffff ioport:e6b00000(size=1048576)
        *-pci:7
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 15.5
             bus info: pci@0000:00:15.5
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:29 memory:fc000000-fc0fffff ioport:e6700000(size=1048576)
        *-pci:8
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 15.6
             bus info: pci@0000:00:15.6
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:30 memory:fbc00000-fbcfffff ioport:e6300000(size=1048576)
        *-pci:9
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 15.7
             bus info: pci@0000:00:15.7
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:31 memory:fb800000-fb8fffff ioport:e5f00000(size=1048576)
        *-pci:10
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 16
             bus info: pci@0000:00:16.0
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:32 ioport:5000(size=4096) memory:fd300000-fd3fffff ioport:e7a00000(size=1048576)
        *-pci:11
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 16.1
             bus info: pci@0000:00:16.1
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:33 ioport:9000(size=4096) memory:fcf00000-fcffffff ioport:e7600000(size=1048576)
        *-pci:12
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 16.2
             bus info: pci@0000:00:16.2
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:34 ioport:d000(size=4096) memory:fcb00000-fcbfffff ioport:e7200000(size=1048576)
        *-pci:13
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 16.3
             bus info: pci@0000:00:16.3
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:35 memory:fc700000-fc7fffff ioport:e6e00000(size=1048576)
        *-pci:14
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 16.4
             bus info: pci@0000:00:16.4
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:36 memory:fc300000-fc3fffff ioport:e6a00000(size=1048576)
        *-pci:15
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 16.5
             bus info: pci@0000:00:16.5
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:37 memory:fbf00000-fbffffff ioport:e6600000(size=1048576)
        *-pci:16
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 16.6
             bus info: pci@0000:00:16.6
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:38 memory:fbb00000-fbbfffff ioport:e6200000(size=1048576)
        *-pci:17
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 16.7
             bus info: pci@0000:00:16.7
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:39 memory:fb700000-fb7fffff ioport:e5e00000(size=1048576)
        *-pci:18
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 17
             bus info: pci@0000:00:17.0
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:40 ioport:6000(size=4096) memory:fd200000-fd2fffff ioport:e7900000(size=1048576)
        *-pci:19
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 17.1
             bus info: pci@0000:00:17.1
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:41 ioport:a000(size=4096) memory:fce00000-fcefffff ioport:e7500000(size=1048576)
        *-pci:20
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 17.2
             bus info: pci@0000:00:17.2
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:42 ioport:e000(size=4096) memory:fca00000-fcafffff ioport:e7100000(size=1048576)
        *-pci:21
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 17.3
             bus info: pci@0000:00:17.3
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:43 memory:fc600000-fc6fffff ioport:e6d00000(size=1048576)
        *-pci:22
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 17.4
             bus info: pci@0000:00:17.4
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:44 memory:fc200000-fc2fffff ioport:e6900000(size=1048576)
        *-pci:23
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 17.5
             bus info: pci@0000:00:17.5
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:45 memory:fbe00000-fbefffff ioport:e6500000(size=1048576)
        *-pci:24
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 17.6
             bus info: pci@0000:00:17.6
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:46 memory:fba00000-fbafffff ioport:e6100000(size=1048576)
        *-pci:25
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 17.7
             bus info: pci@0000:00:17.7
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:47 memory:fb600000-fb6fffff ioport:e5d00000(size=1048576)
        *-pci:26
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 18
             bus info: pci@0000:00:18.0
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:48 ioport:7000(size=4096) memory:fd100000-fd1fffff ioport:e7800000(size=1048576)
        *-pci:27
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 18.1
             bus info: pci@0000:00:18.1
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:49 ioport:b000(size=4096) memory:fcd00000-fcdfffff ioport:e7400000(size=1048576)
        *-pci:28
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 18.2
             bus info: pci@0000:00:18.2
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:50 memory:fc900000-fc9fffff ioport:e7000000(size=1048576)
        *-pci:29
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 18.3
             bus info: pci@0000:00:18.3
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:51 memory:fc500000-fc5fffff ioport:e6c00000(size=1048576)
        *-pci:30
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 18.4
             bus info: pci@0000:00:18.4
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:52 memory:fc100000-fc1fffff ioport:e6800000(size=1048576)
        *-pci:31
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 18.5
             bus info: pci@0000:00:18.5
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:53 memory:fbd00000-fbdfffff ioport:e6400000(size=1048576)
        *-pci:32
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 18.6
             bus info: pci@0000:00:18.6
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:54 memory:fb900000-fb9fffff ioport:e6000000(size=1048576)
        *-pci:33
             description: PCI bridge
             product: PCI Express Root Port
             vendor: VMware
             physical id: 18.7
             bus info: pci@0000:00:18.7
             version: 01
             width: 32 bits
             clock: 33MHz
             capabilities: pci pm pciexpress msi normal_decode bus_master cap_list
             configuration: driver=pcieport
             resources: irq:55 memory:fb500000-fb5fffff ioport:e5c00000(size=1048576)
  *-remoteaccess UNCLAIMED
       vendor: Intel
       physical id: 1
       capabilities: inbound
  *-input:0
       product: Power Button
       physical id: 2
       logical name: input0
       logical name: /dev/input/event0
       capabilities: platform
  *-input:1
       product: AT Translated Set 2 keyboard
       physical id: 3
       logical name: input1
       logical name: /dev/input/event1
       logical name: input1::capslock
       logical name: input1::numlock
       logical name: input1::scrolllock
       capabilities: i8042
  *-input:2
       product: VirtualPS/2 VMware VMMouse
       physical id: 4
       logical name: input3
       logical name: /dev/input/event3
       logical name: /dev/input/mouse1
       capabilities: i8042
  *-input:3
       product: VirtualPS/2 VMware VMMouse
       physical id: 5
       logical name: input4
       logical name: /dev/input/event2
       logical name: /dev/input/mouse0
       capabilities: i8042
