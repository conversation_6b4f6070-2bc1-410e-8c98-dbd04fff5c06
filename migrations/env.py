from __future__ import with_statement

import logging
from logging.config import fileConfig

from flask import current_app

from alembic import context

# Alembic 配置
config = context.config

# 日志配置
fileConfig(config.config_file_name)
logger = logging.getLogger('alembic.env')

# 设置数据库连接字符串
config.set_main_option(
    'sqlalchemy.url',
    current_app.config['SQLALCHEMY_DATABASE_URI'].replace('%', '%%')
)

# 设置模型的元数据
target_metadata = current_app.extensions['migrate'].db.metadata

def run_migrations_offline():
    """离线模式迁移"""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url, target_metadata=target_metadata, literal_binds=True
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online():
    """在线模式迁移"""

    def process_revision_directives(context, revision, directives):
        if getattr(config.cmd_opts, 'autogenerate', False):
            script = directives[0]
            if script.upgrade_ops.is_empty():
                directives[:] = []
                logger.info('No changes in schema detected.')

    connectable = current_app.extensions['migrate'].db.get_engine()

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            process_revision_directives=process_revision_directives,
            dialect_name="dm",
            **current_app.extensions['migrate'].configure_args
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
