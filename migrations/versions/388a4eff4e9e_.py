"""empty message

Revision ID: 388a4eff4e9e
Revises: 
Create Date: 2025-07-04 14:21:26.499355

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '388a4eff4e9e'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('CATEGORY',
    sa.Column('name', sa.String(length=128), nullable=True, comment='资源分类名称'),
    sa.Column('id', sa.String(length=64), nullable=False, comment='主键UUID'),
    sa.Column('create_time', sa.TIMESTAMP(), nullable=False, comment='创建时间'),
    sa.Column('update_time', sa.TIMESTAMP(), nullable=False, comment='更新时间'),
    sa.Column('deleted_time', sa.TIMESTAMP(), nullable=True, comment='删除时间'),
    sa.<PERSON>KeyConstraint('id')
    )
    op.create_table('ORGANIZATIONS',
    sa.Column('_parent', sa.String(length=128), nullable=True, comment='父机构'),
    sa.Column('code', sa.String(length=128), nullable=False, comment='代码'),
    sa.Column('name', sa.String(length=128), nullable=False, comment='名称'),
    sa.Column('fullname', sa.String(length=128), nullable=True, comment='机构全名'),
    sa.Column('description', sa.String(length=512), nullable=True, comment='描述'),
    sa.Column('is_disabled', sa.Boolean(), nullable=True, comment='禁用'),
    sa.Column('_organization', sa.String(length=128), nullable=True, comment='机构'),
    sa.Column('is_virtual_org', sa.Boolean(), nullable=True, comment='是否虚拟组织'),
    sa.Column('com_level', sa.String(length=32), nullable=True, comment='隶属公司层级'),
    sa.Column('com_name', sa.String(length=256), nullable=True, comment='隶属公司名称'),
    sa.Column('com_code', sa.String(length=64), nullable=True, comment='隶属公司编码'),
    sa.Column('org_status', sa.String(length=32), nullable=True, comment='机构状态'),
    sa.Column('org_level', sa.String(length=32), nullable=True, comment='机构层级'),
    sa.Column('parent_org_code', sa.String(length=64), nullable=True, comment='父级机构编号'),
    sa.Column('id', sa.String(length=64), nullable=False, comment='主键UUID'),
    sa.Column('create_time', sa.TIMESTAMP(), nullable=False, comment='创建时间'),
    sa.Column('update_time', sa.TIMESTAMP(), nullable=False, comment='更新时间'),
    sa.Column('deleted_time', sa.TIMESTAMP(), nullable=True, comment='删除时间'),
    sa.ForeignKeyConstraint(['_parent'], ['ORGANIZATIONS.code'], ),
    sa.ForeignKeyConstraint(['parent_org_code'], ['ORGANIZATIONS.code'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('ROLES',
    sa.Column('name', sa.String(length=100), nullable=True, comment='角色名称'),
    sa.Column('description', sa.String(length=100), nullable=True, comment='角色描述'),
    sa.Column('id', sa.String(length=64), nullable=False, comment='主键UUID'),
    sa.Column('create_time', sa.TIMESTAMP(), nullable=False, comment='创建时间'),
    sa.Column('update_time', sa.TIMESTAMP(), nullable=False, comment='更新时间'),
    sa.Column('deleted_time', sa.TIMESTAMP(), nullable=True, comment='删除时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('ROLE_ASSIGN',
    sa.Column('module', sa.String(length=64), nullable=False, comment='模块, assessment:企业考核，certification:产品认证, surveillance:质量监督, inspection:监督抽查'),
    sa.Column('com_name', sa.String(length=100), nullable=False, comment='公司名称'),
    sa.Column('com_code', sa.String(length=100), nullable=False, comment='公司编码'),
    sa.Column('user_code', sa.String(length=100), nullable=True, comment='用户名'),
    sa.Column('user_name', sa.String(length=100), nullable=True, comment='全名'),
    sa.Column('is_leader', sa.Boolean(), nullable=True, comment='是否领导'),
    sa.Column('is_principal', sa.Boolean(), nullable=True, comment='是否负责人'),
    sa.Column('is_supervisor', sa.Boolean(), nullable=True, comment='是否主管'),
    sa.Column('is_admin', sa.Boolean(), nullable=True, comment='是否管理员'),
    sa.Column('id', sa.String(length=64), nullable=False, comment='主键UUID'),
    sa.Column('create_time', sa.TIMESTAMP(), nullable=False, comment='创建时间'),
    sa.Column('update_time', sa.TIMESTAMP(), nullable=False, comment='更新时间'),
    sa.Column('deleted_time', sa.TIMESTAMP(), nullable=True, comment='删除时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('task_logs',
    sa.Column('task_name', sa.String(length=50), nullable=False, comment='任务名称 pullTask:增量, syncTask:全量'),
    sa.Column('start_time', sa.DateTime(), nullable=False, comment='任务开始时间'),
    sa.Column('end_time', sa.DateTime(), nullable=True, comment='任务结束时间'),
    sa.Column('status', sa.String(length=20), nullable=True, comment="任务状态: 'success' or 'failed', 'running'"),
    sa.Column('message', sa.Text(), nullable=True, comment='任务信息'),
    sa.Column('total_records', sa.Integer(), nullable=True, comment='更新或新增的数据总条目'),
    sa.Column('token_id', sa.String(length=100), nullable=True, comment='token_id'),
    sa.Column('id', sa.String(length=64), nullable=False, comment='主键UUID'),
    sa.Column('create_time', sa.TIMESTAMP(), nullable=False, comment='创建时间'),
    sa.Column('update_time', sa.TIMESTAMP(), nullable=False, comment='更新时间'),
    sa.Column('deleted_time', sa.TIMESTAMP(), nullable=True, comment='删除时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('PERMISSIONS',
    sa.Column('name', sa.String(length=100), nullable=False, comment='权限名称'),
    sa.Column('description', sa.String(length=100), nullable=True, comment='权限描述'),
    sa.Column('category_id', sa.String(length=64), nullable=True, comment='类别ID'),
    sa.Column('id', sa.String(length=64), nullable=False, comment='主键UUID'),
    sa.Column('create_time', sa.TIMESTAMP(), nullable=False, comment='创建时间'),
    sa.Column('update_time', sa.TIMESTAMP(), nullable=False, comment='更新时间'),
    sa.Column('deleted_time', sa.TIMESTAMP(), nullable=True, comment='删除时间'),
    sa.ForeignKeyConstraint(['category_id'], ['CATEGORY.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id', name='PERMISSION_ID_IDX')
    )
    op.create_index('PERMISSION_NAME_IDX', 'PERMISSIONS', ['name'], unique=False)
    op.create_table('USERS',
    sa.Column('username', sa.String(length=50), nullable=False, comment='用户名,长度限制为 50 个字符,不能为 NULL'),
    sa.Column('nickname', sa.String(length=100), nullable=True, comment='昵称,长度限制为 100 个字符'),
    sa.Column('email', sa.String(length=256), nullable=True, comment='电子邮件'),
    sa.Column('tel', sa.String(length=64), nullable=True, comment='手机号码'),
    sa.Column('password', sa.Text(), nullable=True, comment='用户密码'),
    sa.Column('app_ids', sa.String(length=64), nullable=True, comment='应用程序 ID 列表'),
    sa.Column('manager', sa.Text(), nullable=True, comment='管理员名称'),
    sa.Column('status', sa.SmallInteger(), nullable=True, comment='用户状态, 0 禁用 1 启用 '),
    sa.Column('auth_type', sa.SmallInteger(), nullable=True, comment='认证类型 1: password, 2: LDAP'),
    sa.Column('last_login', sa.Integer(), nullable=True, comment='上次登录时间(Unix 时间戳)'),
    sa.Column('profile', sa.Text(), nullable=True, comment='用户个人资料(JSON 格式)'),
    sa.Column('fullname', sa.String(length=64), nullable=False, comment='用户全名'),
    sa.Column('is_disabled', sa.Boolean(), nullable=True, comment='是否禁用'),
    sa.Column('is_locked', sa.Boolean(), nullable=True, comment='是否锁定'),
    sa.Column('create_at', sa.TIMESTAMP(), nullable=True, comment='创建日期'),
    sa.Column('update_at', sa.TIMESTAMP(), nullable=True, comment='更新日期'),
    sa.Column('is_system', sa.Boolean(), nullable=True, comment='是否为系统账号'),
    sa.Column('is_public', sa.Boolean(), nullable=True, comment='是否为公共账号'),
    sa.Column('is_master', sa.Boolean(), nullable=True, comment='是否为主账号'),
    sa.Column('sex', sa.SmallInteger(), nullable=True, comment='性别 1 男 2 女'),
    sa.Column('birthday', sa.Date(), nullable=True, comment='生日'),
    sa.Column('dep_level', sa.String(length=64), nullable=True, comment='部门级别'),
    sa.Column('dep_name', sa.String(length=64), nullable=True, comment='部门名称'),
    sa.Column('dep_code', sa.String(length=64), nullable=True, comment='部门编码'),
    sa.Column('com_level', sa.String(length=64), nullable=True, comment='公司级别'),
    sa.Column('com_name', sa.String(length=64), nullable=True, comment='公司名称'),
    sa.Column('com_code', sa.String(length=64), nullable=True, comment='公司编码'),
    sa.Column('job_name', sa.String(length=64), nullable=True, comment='职位名称'),
    sa.Column('job_code', sa.String(length=64), nullable=True, comment='职位编码'),
    sa.Column('office_phone', sa.String(length=20), nullable=True, comment='办公电话'),
    sa.Column('identity_card', sa.String(length=18), nullable=True, comment='身份证号码'),
    sa.Column('id', sa.String(length=64), nullable=False, comment='主键UUID'),
    sa.Column('create_time', sa.TIMESTAMP(), nullable=False, comment='创建时间'),
    sa.Column('update_time', sa.TIMESTAMP(), nullable=False, comment='更新时间'),
    sa.Column('deleted_time', sa.TIMESTAMP(), nullable=True, comment='删除时间'),
    sa.ForeignKeyConstraint(['com_code'], ['ORGANIZATIONS.code'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('username')
    )
    op.create_table('task_failures',
    sa.Column('task_log_id', sa.String(length=64), nullable=False, comment='关联的任务日志ID'),
    sa.Column('record_id', sa.String(length=64), nullable=True, comment='失败记录的ID'),
    sa.Column('failure_reason', sa.Text(), nullable=False, comment='失败原因'),
    sa.Column('failure_detail', sa.Text(), nullable=True, comment='失败详细信息'),
    sa.Column('data_type', sa.String(length=50), nullable=True, comment='数据类型'),
    sa.Column('try_count', sa.Integer(), nullable=True, comment='尝试次数'),
    sa.Column('row_data', sa.Text(), nullable=True, comment='失败时的行数据，JSON格式'),
    sa.Column('result', sa.String(length=50), nullable=True, comment='结果'),
    sa.Column('id', sa.String(length=64), nullable=False, comment='主键UUID'),
    sa.Column('create_time', sa.TIMESTAMP(), nullable=False, comment='创建时间'),
    sa.Column('update_time', sa.TIMESTAMP(), nullable=False, comment='更新时间'),
    sa.Column('deleted_time', sa.TIMESTAMP(), nullable=True, comment='删除时间'),
    sa.ForeignKeyConstraint(['task_log_id'], ['task_logs.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('RESOURCES',
    sa.Column('match_type', sa.String(length=100), nullable=True, comment='匹配类型'),
    sa.Column('name', sa.String(length=100), nullable=False, comment='资源名称'),
    sa.Column('name_len', sa.Integer(), nullable=True, comment='名称长度'),
    sa.Column('priority', sa.Integer(), nullable=True, comment='优先级'),
    sa.Column('action', sa.String(length=100), nullable=True, comment='资源动作'),
    sa.Column('perm_id', sa.String(length=64), nullable=True, comment='权限ID'),
    sa.Column('id', sa.String(length=64), nullable=False, comment='主键UUID'),
    sa.Column('create_time', sa.TIMESTAMP(), nullable=False, comment='创建时间'),
    sa.Column('update_time', sa.TIMESTAMP(), nullable=False, comment='更新时间'),
    sa.Column('deleted_time', sa.TIMESTAMP(), nullable=True, comment='删除时间'),
    sa.ForeignKeyConstraint(['perm_id'], ['PERMISSIONS.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name', 'match_type', name='RESOURCE_NAME_IDX')
    )
    op.create_table('ROLE_PERMISSION',
    sa.Column('role_id', sa.String(length=100), nullable=False, comment='角色ID'),
    sa.Column('perm_id', sa.String(length=100), nullable=False, comment='权限ID'),
    sa.ForeignKeyConstraint(['perm_id'], ['PERMISSIONS.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['ROLES.id'], ),
    sa.PrimaryKeyConstraint('role_id', 'perm_id')
    )
    op.create_table('USER_ROLE',
    sa.Column('username', sa.String(length=100), nullable=False, comment='用户名'),
    sa.Column('role_id', sa.String(length=100), nullable=False, comment='角色 ID'),
    sa.ForeignKeyConstraint(['role_id'], ['ROLES.id'], ),
    sa.ForeignKeyConstraint(['username'], ['USERS.username'], ),
    sa.PrimaryKeyConstraint('username', 'role_id')
    )
    op.create_table('role_permission',
    sa.Column('ROLE_ID', sa.String(length=100), nullable=False),
    sa.Column('PERM_ID', sa.String(length=100), nullable=False),
    sa.ForeignKeyConstraint(['PERM_ID'], ['PERMISSIONS.id'], ),
    sa.ForeignKeyConstraint(['ROLE_ID'], ['ROLES.id'], ),
    sa.PrimaryKeyConstraint('ROLE_ID', 'PERM_ID')
    )
    op.create_table('user_role',
    sa.Column('USERNAME', sa.String(length=100), nullable=False),
    sa.Column('ROLE_ID', sa.String(length=100), nullable=False),
    sa.ForeignKeyConstraint(['ROLE_ID'], ['ROLES.id'], ),
    sa.ForeignKeyConstraint(['USERNAME'], ['USERS.username'], ),
    sa.PrimaryKeyConstraint('USERNAME', 'ROLE_ID')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_role')
    op.drop_table('role_permission')
    op.drop_table('USER_ROLE')
    op.drop_table('ROLE_PERMISSION')
    op.drop_table('RESOURCES')
    op.drop_table('task_failures')
    op.drop_table('USERS')
    op.drop_index('PERMISSION_NAME_IDX', table_name='PERMISSIONS')
    op.drop_table('PERMISSIONS')
    op.drop_table('task_logs')
    op.drop_table('ROLE_ASSIGN')
    op.drop_table('ROLES')
    op.drop_table('ORGANIZATIONS')
    op.drop_table('CATEGORY')
    # ### end Alembic commands ###
