"""Initial PostgreSQL migration

Revision ID: 507750e6d9ba
Revises: 388a4eff4e9e
Create Date: 2025-07-04 17:13:53.497506

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '507750e6d9ba'
down_revision = '388a4eff4e9e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('PERMISSIONS', schema=None) as batch_op:
        batch_op.create_unique_constraint('PERMISSION_ID_IDX', ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('PERMISSIONS', schema=None) as batch_op:
        batch_op.drop_constraint('PERMISSION_ID_IDX', type_='unique')

    # ### end Alembic commands ###
