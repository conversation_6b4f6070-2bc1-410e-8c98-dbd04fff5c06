CREATE TABLE user_center.users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自动递增的主键',
    username VARCHAR(64) NOT NULL UNIQUE COMMENT '用户名，长度限制为 50 个字符，不能为 NULL',
    nickname TEXT COMMENT '昵称，长度限制为 100 个字符',
    email VARCHAR(120) COMMENT '电子邮件',
    tel TEXT COMMENT '手机号码',
    password VARCHAR(64) COMMENT '用户密码',
    app_ids TEXT COMMENT '应用程序 ID 列表（逗号分隔）',
    manager TEXT COMMENT '管理员名称',
    status VARCHAR(10) COMMENT '用户状态, 0 启用 -1 禁用',
    auth_type SMALLINT DEFAULT 1 COMMENT '认证类型',
    last_login INTEGER COMMENT '上次登录时间（Unix 时间戳）',
    profile TEXT COMMENT '用户个人资料（JSON 格式）',
    create_time INTEGER COMMENT '创建时间（Unix 时间戳）',
    update_time INTEGER COMMENT '更新时间（Unix 时间戳）',
    organization VARCHAR(255) COMMENT '用户所属的组织名称',
    fullname VARCHAR(64) COMMENT '用户全名',
    is_disabled BOOLEAN DEFAULT FALSE COMMENT '是否禁用',
    is_locked BOOLEAN DEFAULT FALSE COMMENT '是否锁定',
    create_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    update_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否为系统账号',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否为公共账号',
    is_master BOOLEAN DEFAULT FALSE COMMENT '是否为主账号',
    datasource VARCHAR(64) COMMENT '数据来源，标识用户的来源渠道',
    sex VARCHAR(10) COMMENT '性别 1 男 2 女',
    birthday DATE COMMENT '生日',
    mobile VARCHAR(128) COMMENT '用户的手机号码',
    orgcode VARCHAR(64) COMMENT '用户所属组织的编码',
    user_upd_at TIMESTAMP COMMENT '用户的最后更新时间',
    parttime_job_name VARCHAR(64) COMMENT '用户的兼职职位名称',
    parttime_job_code VARCHAR(64) COMMENT '用户的兼职职位编码',
    parttime_dep_level VARCHAR(64) COMMENT '用户兼职所在部门的级别',
    parttime_dep_name VARCHAR(64) COMMENT '用户兼职所在部门的名称',
    parttime_dep_code VARCHAR(64) COMMENT '用户兼职所在部门的编码',
    parttime_com_level VARCHAR(64) COMMENT '用户兼职所在公司的级别',
    parttime_com_name VARCHAR(64) COMMENT '用户兼职所在公司的名称',
    parttime_com_code VARCHAR(64) COMMENT '用户兼职所在公司的编码',
    dep_level VARCHAR(64) COMMENT '部门级别',
    dep_name VARCHAR(64) COMMENT '部门名称',
    dep_code VARCHAR(64) COMMENT '部门编码',
    com_level VARCHAR(64) COMMENT '公司级别',
    com_name VARCHAR(64) COMMENT '公司名称',
    com_code VARCHAR(64) COMMENT '公司编码',
    job_name VARCHAR(64) COMMENT '职位名称',
    job_code VARCHAR(64) COMMENT '职位编码',
    office_phone VARCHAR(60) COMMENT '办公电话',
    identity_card VARCHAR(18) COMMENT '身份证号码',
    parttime_organization VARCHAR(64) COMMENT '用户所属的兼职组织',
    type_id VARCHAR(64) COMMENT '用户的类型ID'
) TABLESPACE user_center;

-- 添加索引
CREATE UNIQUE INDEX idx_users_username ON user_center.users (username);

-- 添加表注释
COMMENT ON TABLE user_center.users IS '用户表';