version: '3.8'

services:
  web:
    image: registry.shtn.com:9002/library/user-center-web:latest
    ports:
      - "8086:80"
    environment:
      - APISIX_URL=http://***********:9080
      - WOLF_URL=http://***********:8087
    #volumes:
     # - ./web/default.conf:/etc/nginx/conf.d/default.conf
    restart: unless-stopped

  admin:
    image: registry.shtn.com:9002/library/user-center-admin:latest
    ports:
      - "8087:${PORT:-5000}"
    restart: unless-stopped
    #volumes:
      #- ./src:/opt/wolf/server/src
    environment:
      - DATABASE_URI=dm://user_center:user_center123456@***********:5236/
      - SECRET_KEY=your-secret-key
      - DEBUG=True
      - ENV=production
      - LOG_LEVEL=INFO
      - LOG_DIR=./logs
      - JWT_SECRET_KEY=helloworld
      - JWT_HEADER_NAME=Authorization
      - JWT_HEADER_TYPE=None
      - JWT_ACCESS_TOKEN_EXPIRES=172800
      - REDIS_URL=redis://:123456@***********:9400/0
      - ZY_OAUTH_HOST=https://iam-t.crsc.isc/idp
      - ZY_OAUTH_CLIENT_ID=AZSQ
      - ZY_OAUTH_CLIENT_SECRET=0f28d10195714d6ba511838ded07099c
      - ZY_OAUTH_RESPONSE_TYPE=code
      - ZY_OAUTH_GRANT_TYPE=authorization_code
      - ZY_OAUTH_REDIRECT_URI=http://***********:8087
      - ZY_OAUTH_LOGOUT_URL=https://iam-t.crsc.isc/idp/profile/OAUTH2/Redirect/GLO
      - ZY_SYNC_DATA_API_URL=https://iam-t.crsc.isc/bim-server/integration/api.json
      - ZY_SYNC_DATA_SYSTEM_CODE=AZSQ
      - ZY_SYNC_DATA_INTEGRATION_KEY=Crsc@AZSQ
