FROM python:3.11-slim

# 更换为清华大学的 APT 源
RUN echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bullseye main non-free contrib" > /etc/apt/sources.list \
    && echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian-security bullseye-security main" >> /etc/apt/sources.list \
    && echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bullseye-updates main non-free contrib" >> /etc/apt/sources.list \
    && apt-get update && apt-get install -y \
    gcc \
    libssl-dev \
    libffi-dev \
    build-essential \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*


# 设置时区为 Asia/Shanghai
ENV TZ=Asia/Shanghai

# 创建符号链接
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建 pip 配置
RUN mkdir /root/.pip && \
    echo "[global]\n\
index-url = https://mirrors.aliyun.com/pypi/simple/\n\
[install]\n\
trusted-host=mirrors.aliyun.com" > /root/.pip/pip.conf

# 切换工作目录
WORKDIR /opt

# 复制依赖文件并安装依赖
COPY ../requirements.txt /opt
RUN pip install --no-cache-dir -r requirements.txt

# 复制其他文件（根据需要取消注释）
# COPY . /opt

# CMD ["bash", "run.sh"]
