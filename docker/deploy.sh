#!/bin/bash

docker pull registry.shtn.com:9002/library/user-center-admin:latest
docker stop user-portal-admin || true
docker rm user-portal-admin || true
docker run --name user-portal-admin -d -p 8081:5000 --restart=always \
        -e TZ="Asia/Shanghai" \
        -e DATABASE_URI="dm://user_center:user_center123456@***********:5236/" \
        -e SECRET_KEY="your-secret-key" \
        -e DEBUG="False" \
        -e ENV="production" \
        -e LOG_LEVEL="INFO" \
        -e LOG_DIR="./logs" \
        -e JWT_SECRET_KEY="helloworld" \
        -e JWT_HEADER_NAME="Authorization" \
        -e JWT_HEADER_TYPE="None" \
        -e JWT_ACCESS_TOKEN_EXPIRES="172800" \
        -e REDIS_URL="redis://:p8sX%40hK6Uz@***********:6379/0" \
        -e ZY_OAUTH_HOST="https://iam-t.crsc.isc/idp" \
        -e ZY_OAUTH_CLIENT_ID="AZSQ" \
        -e ZY_OAUTH_CLIENT_SECRET="0f28d10195714d6ba511838ded07099c" \
        -e ZY_OAUTH_RESPONSE_TYPE="code" \
        -e ZY_OAUTH_GRANT_TYPE="authorization_code" \
        -e ZY_OAUTH_REDIRECT_URI="http://***********:8087" \
        -e ZY_OAUTH_LOGOUT_URL="https://iam-t.crsc.isc/idp/profile/OAUTH2/Redirect/GLO" \
        -e ZY_SYNC_DATA_API_URL="https://iam-t.crsc.isc/bim-server/integration/api.json" \
        -e ZY_SYNC_DATA_SYSTEM_CODE="AZSQ" \
        -e ZY_SYNC_DATA_INTEGRATION_KEY="Crsc@AZSQ" \
        registry.shtn.com:9002/library/user-center-admin:latest