#!/bin/bash

# 设置变量
IMAGE_NAME="registry.shtn.com:9002/library/user-center-admin"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DOCKERFILE_PATH="$SCRIPT_DIR/Dockerfile"
BUILD_DIR="$(dirname $SCRIPT_DIR)"  # 项目根目录
HOSTS=("$@")     # 获取命令行参数，作为主机列表

# 错误处理函数
function handle_error() {
  echo "❌ Error occurred in step: $1"
  exit 1
}

# 构建 Docker 镜像
echo "Building Docker image..."
docker build -t $IMAGE_NAME -f $DOCKERFILE_PATH $BUILD_DIR || handle_error "Building Docker image"

# 推送镜像到仓库
echo "Pushing Docker image to registry..."
docker push $IMAGE_NAME || handle_error "Pushing Docker image to registry"

# 完成 Docker 构建和推送
echo "✅ Docker image $IMAGE_NAME built and pushed successfully!"

# 如果有主机参数，执行更新
if [ ${#HOSTS[@]} -gt 0 ]; then
  for HOST in "${HOSTS[@]}"; do
    echo "Updating Docker image on server: $HOST"
    ssh "$HOST" "cd /opt/user-center && docker-compose pull && docker-compose up -d" || handle_error "Updating server: $HOST"
  done
else
  echo "No hosts specified. Skipping server update."
fi

# 完成
if [ ${#HOSTS[@]} -gt 0 ]; then
  echo "✅ Update completed on hosts: ${HOSTS[*]}"
else
  echo "No updates performed on any server."
fi
