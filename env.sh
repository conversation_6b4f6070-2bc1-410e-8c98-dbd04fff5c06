# 数据库配置
export DATABASE_URI='***************************************************/user_center'
export SQLALCHEMY_TRACK_MODIFICATIONS=False
export SQLALCHEMY_ECHO=True

# 应用配置
export SECRET_KEY='your-secret-key'
export ENV='development'
export DEBUG=True

# 日志配置
export LOG_LEVEL='DEBUG'
export LOG_DIR='./logs'

# JWT配置
export JWT_SECRET_KEY='helloworld'
export JWT_ACCESS_TOKEN_EXPIRES=86400

# Redis配置
export REDIS_URL="redis://:Redis!23@************:6379/0"

# 管理密码
export MANAGE_PASSWORD='!helloworld$_321'

# 竹云OAuth配置
export ZY_OAUTH_HOST='https://iam-t.crsc.isc/idp'
export ZY_OAUTH_CLIENT_ID='AZSQ'
export ZY_OAUTH_CLIENT_SECRET='0f28d10195714d6ba511838ded07099c'
export ZY_OAUTH_RESPONSE_TYPE='code'
export ZY_OAUTH_GRANT_TYPE='authorization_code'
export ZY_OAUTH_REDIRECT_URI=''
export ZY_OAUTH_LOGOUT_URL='https://iam-t.crsc.isc/idp/profile/OAUTH2/Redirect/GLO'

# 竹云数据同步配置
export ZY_SYNC_DATA_API_URL='https://iam-t.crsc.isc/bim-server/integration/api.json'
export ZY_SYNC_DATA_SYSTEM_CODE='AZSQ'
export ZY_SYNC_DATA_INTEGRATION_KEY='Crsc@AZSQ'