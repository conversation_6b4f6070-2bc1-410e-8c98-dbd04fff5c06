# commands.py
import logging
import click
from flask import current_app
from flask.cli import with_appcontext

from app.service.sync_data import SyncDataService
from app.extension import db  # 导入db对象
from app.utils.redis_utils import UserRedisUtils, ResourceRedisUtils, OrganizationRedisUtils

logger = logging.getLogger('flask_app')

def init_commands(app):
    app.cli.add_command(sync_task)
    app.cli.add_command(pull_task)
    app.cli.add_command(sync_task_logout)
    app.cli.add_command(clear_redis)

@click.command("sync-task")
@click.option("--token_id", type=str, help="Token Id", default="")
@with_appcontext
def sync_task(token_id):
    """执行全量数据同步任务"""
    logger.info("=============== Start Sync Task ===============")
    sync_service = SyncDataService(token_id=token_id)
    try:
        sync_service.sync_task(current_app) 
        # 确保所有数据库操作都已提交
        db.session.commit()
    except Exception as e:
        logger.error(e, exc_info=True)
        # 出现异常时，也要确保所有操作都被提交
        try:
            db.session.commit()
        except Exception:
            pass
    finally:
        # 登出前确保所有数据库变更都已提交
        try:
            db.session.commit()
        except Exception:
            pass
        sync_service.logout()
        logger.info("=============== Finished Sync Task ===============")


@click.command("pull-task")
@click.option("--token_id", type=str, help="Token Id", default="")
@with_appcontext
def pull_task(token_id):
    """执行增量数据同步任务"""
    logger.info("=============== Start Pull Task ===============")
    sync_service = SyncDataService(token_id=token_id)
    try:
        sync_service.pull_task(current_app) 
        # 确保所有数据库操作都已提交
        db.session.commit()
    except Exception as e:
        logger.error(e, exc_info=True)
        # 出现异常时，也要确保所有操作都被提交
        try:
            db.session.commit()
        except Exception:
            pass
    finally:
        # 登出前确保所有数据库变更都已提交
        try:
            db.session.commit()
        except Exception:
            pass
        sync_service.logout()
        logger.info("=============== Finished Pull Task ===============")

@click.command("sync-task-logout")
@click.option("--token_id", type=str, help="Token Id to logout", required=True)
@with_appcontext
def sync_task_logout(token_id):
    """登出同步服务"""
    logger.info("=============== Start Logout Task ===============")
    if not token_id: 
        logger.error("Invalid token id")
        return
    sync_service = SyncDataService(token_id=token_id)
    try:
        result = sync_service.logout()
        if result:
            logger.info(f"成功登出 token: {token_id}")
        else:
            logger.error(f"登出失败 token: {token_id}")
    except Exception as e:
        logger.error(e, exc_info=True)
    finally:
        logger.info("=============== Finished Logout Task ===============")

@click.command("clear-redis")
@click.option("--type", type=str, help="Redis type to clear (user/resource/organization)", required=True)
@with_appcontext
def clear_redis(type):
    """清空指定类型的Redis数据"""
    logger.info("=============== Start Clear Redis Task ===============")
    
    valid_types = ["user", "resource", "organization"]
    if type not in valid_types:
        logger.error(f"无效的Redis类型: {type}. 有效类型: {', '.join(valid_types)}")
        return
    
    try:
        if type == "user":
            success, message = UserRedisUtils.flush_all()
            if success:
                logger.info(f"清空用户Redis数据成功: {message}")
            else:
                logger.error(f"清空用户Redis数据失败: {message}")
        elif type == "resource":
            success, message = ResourceRedisUtils.flush_all()
            if success:
                logger.info(f"清空资源Redis数据成功: {message}")
            else:
                logger.error(f"清空资源Redis数据失败: {message}")
        elif type == "organization":
            success, message = OrganizationRedisUtils.flush_all()
            if success:
                logger.info(f"清空组织Redis数据成功: {message}")
            else:
                logger.error(f"清空组织Redis数据失败: {message}")
    except Exception as e:
        logger.error(f"清空Redis数据时发生异常: {e}", exc_info=True)
    
    logger.info("=============== Finished Clear Redis Task ===============")
