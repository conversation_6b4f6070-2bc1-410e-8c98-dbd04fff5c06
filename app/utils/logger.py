import logging
from logging.handlers import TimedRotatingFileHandler
import os

def setup_logger(log_dir='./logs', log_level=logging.DEBUG):
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    logger = logging.getLogger('flask_app')
    logger.setLevel(log_level)

    file_handler = TimedRotatingFileHandler(
        filename=os.path.join(log_dir, 'app.log'),
        when='midnight',
        backupCount=7,
        encoding='utf-8'
    )
    console_handler = logging.StreamHandler()

    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - [%(filename)s:%(module)s:%(funcName)s:%(lineno)d] - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

def setup_task_logger(log_dir='./logs/task', log_level=logging.DEBUG):
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    logger = logging.getLogger('task_logger')
    logger.setLevel(log_level)

    file_handler = TimedRotatingFileHandler(
        filename=os.path.join(log_dir, 'app.log'),
        when='midnight',
        backupCount=7,
        encoding='utf-8'
    )
    console_handler = logging.StreamHandler()

    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - [%(filename)s:%(module)s:%(funcName)s:%(lineno)d] - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger