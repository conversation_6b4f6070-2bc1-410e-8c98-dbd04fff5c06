class ArgsError(Exception):
    def __init__(self, message, field=None, code=400):
        """
        初始化异常
        :param message: 错误信息
        :param field: 出错的字段（可选）
        :param code: HTTP 状态码（默认 400）
        """
        super().__init__(message)
        self.message = message
        self.field = field
        self.code = code

    def to_dict(self):
        """将异常信息转换为字典"""
        error_data = {
            'message': self.message,
            'code': self.code
        }
        if self.field:
            error_data['field'] = self.field
        return error_data