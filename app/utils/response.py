import json
from flask import Flask, Response, jsonify, request

def _json(platform, code, data, reason, errmsg):
    jso = {}
    if platform == "userportal-web":
        jso["code"] = code
        jso["success"] = code == 200
        jso["message"] = errmsg or ""
        jso["result"] = {} if data is None or data == '' else data
    elif platform == "process-web":
        jso["code"] = 2000 if code == 200 else 40001
        jso["msg"] = errmsg or ""
        jso["data"] = {} if data is None or data == '' else data
    else:
        jso["code"] = code
        jso["ok"] = code == 200
        jso["success"] = code == 200
        jso["reason"] = reason or ""
        jso["errmsg"] = errmsg or ""
        jso["data"] = {} if data is None or data == '' else data
    return jso

def json_response(portal, code, data=None, reason=None):
    response = _json(portal, code, data, reason, None)
    return jsonify(response), code

def success_response(data=None):
    portal = request.headers.get('X-Request-From', '').lower()
    response = _json(portal, 200, data, None, None)
    return jsonify(response)

def fail_response(code, errmsg=None, data=None):
    portal = request.headers.get('X-Request-From', '').lower()
    response = _json(portal, code, data, errmsg, errmsg)
    return Response(json.dumps(response), status=code, mimetype='application/json')
    # if not portal:
    #     return jsonify(response), code
    # return jsonify(response), code
