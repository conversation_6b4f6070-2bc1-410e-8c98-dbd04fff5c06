import logging
from datetime import datetime
from flask import request
from app.utils.errors import ArgsError  # 自定义异常类
from app.utils.common import unixtime  # 工具函数
from sqlalchemy import between

logger = logging.getLogger('flask_app')


class ArgsHelper:
    @staticmethod
    def get_args():
        """获取当前的请求参数"""
        if request.method == 'GET':
            return request.args.to_dict()
        return request.json or {}

    @staticmethod
    def get_required_arg(field):
        """获取必需参数"""
        args = ArgsHelper.get_args()
        value = args.get(field)
        if value is None or value == '':
            logger.error(f"Request arg [{field}] missing")
            raise ArgsError(f"{field} is required.")
        return value

    @staticmethod
    def get_required_string_arg(field):
        """获取必需字符串参数"""
        value = ArgsHelper.get_required_arg(field)
        if not isinstance(value, str):
            logger.error(f"Request arg [{field}] invalid")
            raise ArgsError(f"{field} must be a string.")
        return value

    @staticmethod
    def get_required_int_arg(field):
        """获取必需整数参数"""
        value = ArgsHelper.get_required_arg(field)
        try:
            return int(value)
        except ValueError:
            logger.error(f"Request arg [{field}] invalid")
            raise ArgsError(f"{field} must be an integer.")

    @staticmethod
    def get_required_float_arg(field):
        """获取必需浮点数参数"""
        value = ArgsHelper.get_required_arg(field)
        try:
            return float(value)
        except ValueError:
            logger.error(f"Request arg [{field}] invalid")
            raise ArgsError(f"{field} must be a float.")

    @staticmethod
    def get_object_arg(field):
        """获取对象参数"""
        value = ArgsHelper.get_arg(field)
        if isinstance(value, str):
            try:
                import json
                value = json.loads(value)
            except json.JSONDecodeError:
                logger.error(f"Request arg [{field}] invalid")
                raise ArgsError(f"{field} must be a valid JSON object.")
        if not isinstance(value, dict):
            raise ArgsError(f"{field} must be an object.")
        return value

    @staticmethod
    def get_required_object_arg(field):
        """获取必需对象参数"""
        value = ArgsHelper.get_required_arg(field)
        if isinstance(value, str):
            try:
                import json
                value = json.loads(value)
            except json.JSONDecodeError:
                raise ArgsError(f"{field} must be a valid JSON object.")
        if not isinstance(value, dict):
            raise ArgsError(f"{field} must be an object.")
        return value

    @staticmethod
    def get_array_arg(field, default_value=None):
        """获取数组参数"""
        value = ArgsHelper.get_arg(field, default_value)
        if isinstance(value, str):
            try:
                import json
                if value.startswith('[') and value.endswith(']'):
                    value = json.loads(value)
                else:
                    value = value.split(',')
            except ValueError:
                logger.error(f"Request arg [{field}] invalid")
                raise ArgsError(f"{field} must be an array.")
        if value is not None and not isinstance(value, list):
            raise ArgsError(f"{field} must be an array.")
        return value

    @staticmethod
    def get_required_array_arg(field):
        """获取必需数组参数"""
        value = ArgsHelper.get_array_arg(field)
        if value is None:
            logger.error(f"Request arg [{field}] missing")
            raise ArgsError(f"{field} is required.")
        return value

    @staticmethod
    def get_required_int_array_arg(field):
        """获取必需的整数数组参数"""
        values = ArgsHelper.get_required_array_arg(field)
        try:
            return [int(v) for v in values]
        except ValueError:
            raise ArgsError(f"{field} must be an array of integers.")

    @staticmethod
    def get_arg(field, default_value=None):
        """获取参数"""
        args = ArgsHelper.get_args()
        return args.get(field, default_value)

    @staticmethod
    def get_string_arg(field, default_value=None):
        """获取字符串参数"""
        value = ArgsHelper.get_arg(field, default_value)
        return str(value) if value is not None else default_value

    @staticmethod
    def get_bool_arg(field, default_value=None):
        """获取布尔值参数"""
        value = ArgsHelper.get_arg(field, default_value)
        return str(value).lower() in ['true', '1', 'yes', 'y'] if value is not None else default_value

    @staticmethod
    def get_datetime_as_unixtime(field, default_value=None):
        """获取时间参数并转换为 Unix 时间戳"""
        value = ArgsHelper.get_arg(field)
        if value:
            return unixtime(value)
        return default_value

    @staticmethod
    def get_start_end_time_args():
        """获取时间范围参数"""
        start_time = ArgsHelper.get_datetime_as_unixtime('startTime')
        end_time = ArgsHelper.get_datetime_as_unixtime('endTime')
        if start_time and end_time:
            return between(start_time, end_time)
        elif start_time:
            return {'gte': start_time}
        elif end_time:
            return {'lte': end_time}
        return None

    @staticmethod
    def get_order_by_args(default_order=None):
        """获取排序参数"""
        sort = ArgsHelper.get_arg('sort', default_order)
        if sort:
            sort = sort.strip()
            if sort.startswith('+'):
                return [(sort[1:], 'ASC')]
            elif sort.startswith('-'):
                return [(sort[1:], 'DESC')]
        return None
