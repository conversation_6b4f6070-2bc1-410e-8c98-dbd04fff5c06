from app.extension import redis_client
def set_key(key, value, ex=None):
    """
    Set a key in Redis with an optional expiration time.
    :param key: The key to set.
    :param value: The value to set.
    :param ex: Expiration time in seconds (optional).
    :return: True if successful, False otherwise.
    """
    try:
        return redis_client.set(key, value, ex=ex)
    except Exception as e:
        # <PERSON>le exception (e.g., log it)
        return False, e

def get_key(key):
    """
    Get a value from Red<PERSON> by key.
    :param key: The key to retrieve.
    :return: The value if found, None otherwise.
    """
    try:
        return redis_client.get(key)
    except Exception as e:
        # Handle exception (e.g., log it)
        return None

def delete_key(key):
    """
    Delete a key from Redis.
    :param key: The key to delete.
    :return: Number of keys deleted (0 or 1).
    """
    try:
        return redis_client.delete(key)
    except Exception as e:
        # Handle exception (e.g., log it)
        return 0

def key_exists(key):
    """
    Check if a key exists in Redis.
    :param key: The key to check.
    :return: True if exists, False otherwise.
    """
    try:
        return redis_client.exists(key) == 1
    except Exception as e:
        # Handle exception (e.g., log it)
        return False

class UserRedisUtils:

    @staticmethod
    def set_userinfo(username, userinfo):
        """
        Set user info in Redis.
        :param username: The username to set.
        :param userinfo: The user info to set.
        :return: True if successful, False otherwise.
        """
        key = f"userInfo:{username}"
        return set_key(key, userinfo, ex=60 * 60 * 24)
    
    @staticmethod
    def get_userinfo(username):
        """
        Get user info from Redis.
        :param username: The username to search for.
        :return: The user info if found, None otherwise.
        """
        key = f"userInfo:{username}"
        userinfo = get_key(key)
        if userinfo:
            return userinfo
        else:
            return None
    @staticmethod
    def set_user_token(username, token):
        """
        Set user token in Redis.
        :param username: The username to set.
        :param token: The token to set.
        :return: True if successful, False otherwise.
        """
        key = f"userToken:{username}"
        return set_key(key, token, ex=60 * 60 * 24)

    @staticmethod
    def get_user_by_username(username):
        """
        Get user by username from Redis.
        :param username: The username to search for.
        :return: The user object if found, None otherwise.
        """
        key = f"userInfo:{username}"
        user_data = get_key(key)
        if user_data:
            return user_data
        else:
            return None
    
    @staticmethod
    def get_user_token(username):
        """
        Get user token by username from Redis.
        :param username: The username to search for.
        :return: The token if found, None otherwise.
        """
        key = f"userToken:{username}"
        token = get_key(key)
        if token:
            return token.decode()
        else:
            return None
    
    @staticmethod
    def delete_user_by_username(username):
        """
        删除 Redis 中的用户信息和 token。
        :param username: 用户名
        :return: 删除的键数量
        """
        key_info = f"userInfo:{username}"
        key_token = f"userToken:{username}"
        try:
            deleted_info = delete_key(key_info)
            deleted_token = delete_key(key_token)
            return deleted_info + deleted_token
        except Exception as e:
            # logger.error(f"删除 Redis 键失败: {e}")
            return 0
    
    @staticmethod
    def flush_all():
        """
        Flush all user-related keys from Redis.
        :return: True if successful, False otherwise.
        """
        try:
            patterns = [
                'userInfo:*',  # 用户信息
                'userToken:*'  # 用户token
            ]
            
            deleted_count = 0
            for pattern in patterns:
                cursor = 0
                while True:
                    cursor, keys = redis_client.scan(cursor, match=pattern)
                    if keys:
                        redis_client.delete(*keys)
                        deleted_count += len(keys)
                    if cursor == 0:
                        break
            
            return True, f"成功删除 {deleted_count} 个用户相关的Redis键"
        except Exception as e:
            return False, f"删除用户Redis数据失败: {e}"


class ResourceRedisUtils:
    
    @staticmethod
    def set_resource_info(resource_name:str, action:str, resource_info):
        """
        Set resource info in Redis.
        :param resource_name: The resource name to set.
        :param resource_info: The resource info to set.
        :return: True if successful, False otherwise.
        """
        key = f"resourceInfo:{resource_name}:{action}"
        return set_key(key, resource_info)
    
    @staticmethod
    def get_resource_info(resource_name, action):
        """
        Get resource info from Redis.
        :param resource_name: The resource name to search for.
        :return: The resource info if found, None otherwise.
        """
        key = f"resourceInfo:{resource_name}:{action}"
        resource_info = get_key(key)
        if resource_info:
            return resource_info
        else:
            return None


    @staticmethod
    def delete_resource_by_name_and_action(name, action):
        """
        Delete resource by name and match type from Redis.
        :param name: The name to delete.
        :param match_type: The match type to delete.
        :return: Number of keys deleted (0 or 1).
        """
        key = f"resourceInfo:{name}:{action}"
        return delete_key(key)
    
    @staticmethod
    def flush_all():
        """
        Flush all resource-related keys from Redis.
        :return: True if successful, False otherwise.
        """
        try:
            deleted_count = 0
            cursor = 0
            pattern = 'resourceInfo:*'  # 要匹配的模式

            while True:
                cursor, keys = redis_client.scan(cursor, match=pattern)
                if keys:
                    redis_client.delete(*keys)  # 删除匹配的键
                    deleted_count += len(keys)
                if cursor == 0:
                    break
            
            return True, f"成功删除 {deleted_count} 个资源相关的Redis键"
        except Exception as e:
            return False, f"删除资源Redis数据失败: {e}"

class OrganizationRedisUtils:

    @staticmethod
    def set_organization_info(org_code, org_info):
        """
        Set organization info in Redis.
        :param org_code: The organization code to set.
        :param org_info: The organization info to set.
        :return: True if successful, False otherwise.
        """
        key = f"organizationInfo:{org_code}"
        return set_key(key, org_info)
    
    @staticmethod
    def get_organization_info(org_code):
        """
        Get organization info from Redis.
        :param org_code: The organization code to search for.
        :return: The organization info if found, None otherwise.
        """
        key = f"organizationInfo:{org_code}"
        org_info = get_key(key)
        if org_info:
            return org_info.decode()
        else:
            return None
    
    @staticmethod
    def delete_organization_by_code(org_code):
        """
        Delete organization by code from Redis.
        :param org_code: The organization code to delete.
        :return: Number of keys deleted (0 or 1).
        """
        key = f"organizationInfo:{org_code}"
        return delete_key(key)
    
    @staticmethod
    def set_all_organizations(org_info):
        """
        Set all organizations in Redis.
        :param org_info: The organization info to set.
        :return: True if successful, False otherwise.
        """
        key = "allOrganizations"
        return set_key(key, org_info)
    @staticmethod
    def set_tree_data(tree_data):
        """
        Set tree data in Redis.
        :param tree_data: The tree data to set.
        :return: True if successful, False otherwise.
        """
        key = "organizationTreeData"
        return set_key(key, tree_data)

    @staticmethod
    def get_tree_data():
        """
        Get tree data from Redis.
        :return: The tree data if found, None otherwise.
        """
        key = "organizationTreeData"
        return get_key(key)
        
    @staticmethod
    def get_all_organizations():
        """
        Get all organizations from Redis.
        :return: The organization info if found, None otherwise.
        """
        key = "allOrganizations"
        org_info = get_key(key)
        if org_info:
            return org_info.decode()
        else:
            return None
    
    @staticmethod
    def flush_all():
        """
        Flush all organization-related keys from Redis.
        :return: True if successful, False otherwise.
        """
        try:
            patterns = [
                'organizationInfo:*',  # 单个组织信息
                'allOrganizations',    # 所有组织数据
                'organizationTreeData' # 组织树形数据
            ]
            
            deleted_count = 0
            for pattern in patterns:
                if ':' not in pattern:
                    # 精确匹配的键
                    if redis_client.exists(pattern):
                        redis_client.delete(pattern)
                        deleted_count += 1
                else:
                    # 模式匹配的键
                    cursor = 0
                    while True:
                        cursor, keys = redis_client.scan(cursor, match=pattern)
                        if keys:
                            redis_client.delete(*keys)
                            deleted_count += len(keys)
                        if cursor == 0:
                            break
            
            return True, f"成功删除 {deleted_count} 个组织相关的Redis键"
        except Exception as e:
            return False, f"删除组织Redis数据失败: {e}"