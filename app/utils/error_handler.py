import logging
from flask import Blueprint, request
from flask import current_app
from app.utils.errors import ArgsError
from .response import fail_response
from app.utils.logger import setup_logger
from werkzeug.exceptions import HTTPException
logger = logging.getLogger('flask_app')

error_bp = Blueprint('errors', __name__)

@error_bp.app_errorhandler(HTTPException)
def handle_http_exception(error):
    """
    处理所有 HTTP 异常
    """
    logger.error(f"HTTPException: {error.description}, Path: {request.path}, Code: {error.code}", exc_info=True)
    return fail_response(error.code, error.description)

@error_bp.app_errorhandler(404)
def not_found_error(error):
    path = request.path
    logger.error(f"resource not found: {path}")
    return fail_response(404, f"[{path}] not found",)

@error_bp.app_errorhandler(500)
def internal_error(error):
    logger.error(error, exc_info=True)
    return fail_response(400, "error")

@error_bp.app_errorhandler(Exception)
def exception_handle(error):
    logger.error(error, exc_info=True)
    msg = f"${error}" if  current_app.config['DEBUG']  else "Internal Occur Error  !!"
    return fail_response(400, msg)

@error_bp.app_errorhandler(ArgsError)
def handle_args_error(error):
    logger.error(error, exc_info=True)
    return fail_response(400, error.message)