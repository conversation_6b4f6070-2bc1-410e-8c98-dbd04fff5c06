import json
import logging
from flask import jsonify, request, g
from flask_jwt_extended import JWTManager, verify_jwt_in_request, get_jwt_identity
from app.models.user import User
from app.utils.redis_utils import UserRedisUtils
from app.utils.response import fail_response

jwt = JWTManager()
logger = logging.getLogger('flask_app')  # 获取全局日志记录器

# 跳过认证的路由白名单（支持路径前缀）
WHITE_LIST = [
    '/favicon.ico',
    '/swagger',          # Swagger 文档根路径
    '/swaggerui',         # Swagger UI 静态资源路径前缀
    '/wolf/user/login',  # 登录接口
    '/wolf/user/loginCallback',
    '/wolf/user/getZhuyunLoginUrl',
    '/wolf/user/getUsersByRoleIds',
    '/wolf/sys/randomImage'
]

def init_jwt(app):
    """初始化 JWT 并添加全局认证逻辑"""
    jwt.init_app(app)

    @app.before_request
    def global_jwt_auth():
        """全局 JWT 认证"""
        logger.debug(f"Request path: {request.path}")
        logger.debug(f"Request From: {request.headers.get('X-Request-From')}")

        # 跳过白名单中的路径
        if any(request.path.startswith(prefix) for prefix in WHITE_LIST):
            logger.info(f"Skipping authentication for {request.path}")
            return  # 白名单路径无需认证

        try:
            g.current_user = {}
            # 验证 JWT 令牌
            userinfo, token = valid_jwt()
            # verify_jwt_in_request()
            # username = get_jwt_identity()  # 将认证用户存储到 g 对象中
            username = userinfo.get('username')
            redis_token = UserRedisUtils.get_user_token(username)
            if not redis_token:
                logger.error(f"Token not found for user: {username}")
                return fail_response(401, "认证失败")
            # 验证token是否失效（单一用户登录）
            # if token != redis_token:
            #     logger.debug(f"Token:{token}")
            #     logger.debug(f"Redis Token:{redis_token}")
            #     logger.error(f"Token mismatch for user: {username}")
            #     return fail_response(401, "认证失败")
            user = UserRedisUtils.get_userinfo(username)
            if not user:
                user = User.get_user_by_username(username=username)
            else:
                user_data = json.loads(user)
                user = User.json_to_user(user_data)
                user.role_ids = user_data.get('role_ids', [])
            # g.user = User.get_user_by_username(username=username)
            if not user:
                return fail_response(401, "获取用户信息失败")    
            g.user = user
            logger.info(f"Authenticated user: {g.user.username}")
        except Exception as e:
            logger.error(f"Authentication failed: {e}", exc_info=True)
            return fail_response(401, "认证失败")


    def valid_jwt():
        token = request.headers.get(app.config['JWT_HEADER_NAME'], '')
        if not token:
            token = request.headers.get('x-rbac-token', '')
        logger.debug(f"JWT:{token}")
        if token.startswith(app.config['JWT_HEADER_TYPE']):
            token = token.split(app.config['JWT_HEADER_TYPE'])[-1] if token else None
        if not token:
            logger.error("Token not found")
            return fail_response(401, "认证失败")
        data =  jwt._decode_jwt_from_config(token)
        logger.debug(f"Decoded JWT data: {data}")
        return data, token
