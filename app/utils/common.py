import bcrypt
import hashlib
import random
import string
from datetime import datetime
import time
import logging
from typing import Optional, List
import re
import pytz

logger = logging.getLogger('flaks_app')

# SHA1 加密
def sha1hex(data: str) -> str:
    sha1 = hashlib.sha1()
    sha1.update(data.encode('utf-8'))
    return sha1.hexdigest()

# 判断是否为数字
def is_numeric(value: str) -> bool:
    try:
        float(value)
        return True
    except ValueError:
        return False

# 随机字符串生成
def random_string(length: int) -> str:
    characters = string.ascii_letters + string.digits
    return ''.join(random.choices(characters, k=length))

# 密码加密
def encode_password(raw_password: str) -> str:
    return bcrypt.hashpw(raw_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

# 密码比较
def compare_password(raw_password: str, encoded_password: str) -> bool:
    return bcrypt.checkpw(raw_password.encode('utf-8'), encoded_password.encode('utf-8'))

# 获取当前时间
def current_date(format: str = '%Y-%m-%d %H:%M:%S') -> str:
    return datetime.now().strftime(format)

# 转换为 Unix 时间戳
def unixtime(str_date: Optional[str] = None) -> int:
    if str_date:
        return int(datetime.strptime(str_date, '%Y-%m-%d %H:%M:%S').timestamp())
    return int(time.time())

# 从 Unix 时间戳转换为日期
def from_unixtime(timestamp: int, format: str = '%Y-%m-%d %H:%M:%S') -> str:
    return datetime.fromtimestamp(timestamp).strftime(format)

# 获取日期部分
def get_date(str_date: str) -> str:
    return datetime.strptime(str_date, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')

# 白名单字段过滤
def filter_field_white(obj: dict, white_fields: List[str]) -> dict:
    return {key: obj[key] for key in white_fields if key in obj}

# 黑名单字段过滤
def filter_field_black(obj: dict, black_fields: List[str]) -> dict:
    return {key: value for key, value in obj.items() if key not in black_fields}

# 格式化字符串
def format_string(template: str, arg_object: dict) -> str:
    return re.sub(r'\${(.*?)}', lambda match: str(arg_object.get(match.group(1), '')), template)

# 解析日期字符串
def parse_datetime(date_string: str, date_format: str = '%Y-%m-%d %H:%M:%S.%f') -> Optional[datetime]:
    if not date_string:
        return None

    try:
        parsed_date = datetime.strptime(date_string, date_format)
        return parsed_date
    except ValueError as e:
        logger.error(f"日期解析错误: data: {date_string}, format: {date_format}, 错误信息: {str(e)}")
        return None


# 转换类属性为字典
def config_to_dict(config_class):
    return {
        key.lower(): value
        for key, value in config_class.__dict__.items()
        if not key.startswith("__") and not callable(value)
    }

def str_to_bool(value):
    if isinstance(value, str):
        value = value.lower()
        if value in ('true', '1'):
            return True
        elif value in ('false', '0'):
            return False
    return bool(value)  # 默认返回 False