import logging
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

from app.commands import init_commands
from app.middleware import setup_middlewares
from app.models import import_model
from .config import Config
from app.utils.logger import setup_logger
from .utils.auth import init_jwt
from app.tasks import setup_schedule
# db = SQLAlchemy()

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    setup_logger(log_dir=app.config.get('LOG_DIR', './logs'), log_level=app.config.get('LOG_LEVEL', logging.DEBUG))

    from app.routes import config_namespaces
    from app.extension import api
    config_namespaces(api)

    from .extension import config_extensions
    config_extensions(app)
    
    from app.utils.error_handler import error_bp
    app.register_blueprint(error_bp)

    init_jwt(app)

    # 注册中间件
    setup_middlewares(app)
    # 引入模型
    import_model()

    # 初始化系统用户（仅在表存在时）
    with app.app_context():
        try:
            from app.models.user import User
            User.init_system_users()
        except Exception as e:
            app.logger.warning(f"跳过系统用户初始化（可能是数据库表不存在）: {e}")

    # 注册命令
    init_commands(app)
    # 启动定时任务
    # setup_schedule(app)
    return app
