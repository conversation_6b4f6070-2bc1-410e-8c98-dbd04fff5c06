# request_logging.py
import logging
from flask import g, request
import time
logger = logging.getLogger('flask_app')
def setup_request_logging(app):
    """设置请求日志记录"""

    @app.before_request
    def start_timer():
        """在每个请求开始之前记录当前时间"""
        g.start = time.time()

    @app.after_request
    def log_request(response):
        """在每个请求结束后计算耗时并记录日志"""
        if not response.is_sequence:
            logger.info(f"Response is not sequence: {request.url}")
            return response
        duration = time.time() - getattr(g, 'start', 0)  # 计算请求耗时
        request_path = request.url  # 获取请求路径
        response_size = len(response.data)  # 获取响应大小（字节数）

        logger.info(
            f"Request path: {request_path}, "
            f"Response size: {response_size} bytes, "
            f"Request took {duration:.2f} seconds, "
            f"Response status: {response.status_code}"
        )
        # 可以将耗时信息添加到响应头中
        response.headers['X-Request-Duration'] = str(duration)
        return response