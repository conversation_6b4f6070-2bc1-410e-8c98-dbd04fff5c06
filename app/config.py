import os

from app.utils.common import str_to_bool

class Config:
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URI')
    SQLALCHEMY_TRACK_MODIFICATIONS = str_to_bool(os.getenv('SQLALCHEMY_TRACK_MODIFICATIONS'))
    SQLALCHEMY_ECHO = str_to_bool(os.getenv('SQLALCHEMY_ECHO'))

    # 应用配置
    SECRET_KEY = os.getenv('SECRET_KEY')
    ENV = os.getenv('ENV')
    DEBUG = str_to_bool(os.getenv('DEBUG'))

    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL')
    LOG_DIR = os.getenv('LOG_DIR')

    # JWT配置
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY')
    JWT_HEADER_NAME = 'Authorization'
    # 自定义请求头名称
    JWT_HEADER_TYPE = 'V1#tonghao_process#'
    # 不需要前缀
    JWT_ACCESS_TOKEN_EXPIRES = int(os.getenv('JWT_ACCESS_TOKEN_EXPIRES', '86400'))

    # Redis配置
    REDIS_URL = os.getenv('REDIS_URL')

    # 管理密码
    MANAGE_PASSWORD = os.getenv('MANAGE_PASSWORD')

class ZhuyunOauthConfig:
    # 竹云OAuth配置
    HOST = os.getenv('ZY_OAUTH_HOST')
    CLIENT_ID = os.getenv('ZY_OAUTH_CLIENT_ID')
    CLIENT_SECRET = os.getenv('ZY_OAUTH_CLIENT_SECRET')
    RESPONSE_TYPE = os.getenv('ZY_OAUTH_RESPONSE_TYPE')
    GRANT_TYPE = os.getenv('ZY_OAUTH_GRANT_TYPE')
    # host +  '/wolf/user/loginCallback'
    REDIRECT_URI = os.getenv('ZY_OAUTH_REDIRECT_URI')
    LOGOUT_URL = os.getenv('ZY_OAUTH_LOGOUT_URL')


class ZhuyunSyncDataConfig:
    # 竹云数据同步配置
    API_URL = os.getenv('ZY_SYNC_DATA_API_URL')
    SYSTEM_CODE = os.getenv('ZY_SYNC_DATA_SYSTEM_CODE')
    INTEGRATION_KEY = os.getenv('ZY_SYNC_DATA_INTEGRATION_KEY')