import os

from app.utils.common import str_to_bool

class Config:
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URI', 'postgresql://user_center:user_center123456@localhost:5432/user_center')
    # SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URI', 'sqlite:///local.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = str_to_bool(os.getenv('SQLALCHEMY_TRACK_MODIFICATIONS', False))
    SQLALCHEMY_ECHO = str_to_bool(os.getenv('SQLALCHEMY_ECHO', False))

    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key')
    ENV = os.getenv('ENV', 'development')
    DEBUG = str_to_bool(os.getenv('DEBUG', ENV == 'development'))

     # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'DEBUG')
    LOG_DIR = os.getenv('LOG_DIR', './logs')

    # JWT配置
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'helloworld')
    JWT_HEADER_NAME = 'Authorization'  
    # 自定义请求头名称
    JWT_HEADER_TYPE = 'V1#tonghao_process#'  
    # 不需要前缀
    JWT_ACCESS_TOKEN_EXPIRES = int(os.getenv('JWT_ACCESS_TOKEN_EXPIRES', f"{86400 * 1}"))
    # Redis配置
    REDIS_URL = os.getenv('REDIS_URL', "redis://:123456@**********:9400/0")
    # 管理密码
    MANAGE_PASSWORD = os.getenv('MANAGE_PASSWORD', '123456')

class ZhuyunOauthConfig:
    HOST = os.getenv('ZY_OAUTH_HOST', 'https://iam-t.crsc.isc/idp')
    CLIENT_ID = os.getenv('ZY_OAUTH_CLIENT_ID', 'AZSQ')
    CLIENT_SECRET = os.getenv('ZY_OAUTH_CLIENT_SECRET', '0f28d10195714d6ba511838ded07099c')
    RESPONSE_TYPE = os.getenv('ZY_OAUTH_RESPONSE_TYPE', 'code')
    GRANT_TYPE = os.getenv('ZY_OAUTH_GRANT_TYPE', 'authorization_code')
    # host +  '/wolf/user/loginCallback'
    REDIRECT_URI = os.getenv('ZY_OAUTH_REDIRECT_URI')
    LOGOUT_URL = os.getenv('ZY_OAUTH_LOGOUT_URL', 'https://iam-t.crsc.isc/idp/profile/OAUTH2/Redirect/GLO')


class ZhuyunSyncDataConfig:
    API_URL = os.getenv('ZY_SYNC_DATA_API_URL', 'https://iam-t.crsc.isc/bim-server/integration/api.json')
    SYSTEM_CODE = os.getenv('ZY_SYNC_DATA_SYSTEM_CODE', 'AZSQ')
    INTEGRATION_KEY = os.getenv('ZY_SYNC_DATA_INTEGRATION_KEY', 'Crsc@AZSQ')