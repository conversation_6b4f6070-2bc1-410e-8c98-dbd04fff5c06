import logging
import os 
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_restx import Api
from flask_redis import FlaskRedis
logger = logging.getLogger('flask_app')
redis_client = FlaskRedis()

db = SQLAlchemy()
migrate = Migrate()
api = Api(
    title="User Center Admin API", 
    version="1.0", 
    description="A simple User Management API", 
    doc='/swagger'
    # doc=('/swagger' if os.getenv('ENV', 'development') == 'development' else None)
)

# 初始化拓展
def config_extensions(app):
    db.init_app(app)
    migrate.init_app(app, db)
    api.init_app(app)  # 初始化 API
    redis_client.init_app(app)
    try:
        response = redis_client.ping()
        if not response:
            logger.error("Redis server did not respond to ping.")
    except Exception as e:
        print(f"Error connecting to Redis server: {e}")
