from app.extension import db
from datetime import datetime
from app.models.base_model import BaseModel
from app.models.permission import Permission
from sqlalchemy import or_, and_, literal, func
from app.utils.constant import MatchType


class Resource(BaseModel):
    __tablename__ = 'RESOURCES'
    __table_args__ = {"schema": "USER_CENTER"}  # 指定表的模式

    match_type = db.Column(db.String(100), nullable=True, comment="匹配类型")
    name = db.Column(db.String(100), nullable=False, comment="资源名称")
    name_len = db.Column(db.Integer, nullable=True, comment="名称长度")
    priority = db.Column(db.Integer, nullable=True, comment="优先级")
    action = db.Column(db.String(100), nullable=True, comment="资源动作")
    perm_id = db.Column(db.String(64), db.<PERSON><PERSON>('PERMISSIONS.id'), nullable=True, comment="权限ID")

    permission = db.relationship(
        "Permission",
        back_populates="resources",
        primaryjoin="Resource.perm_id == Permission.id",
        lazy='joined'
    )

    def to_dict(self):
        info = super().to_dict()
        info['permission'] = self.permission.to_dict() if self.permission else {}
        return info

    __table_args__ = (
        db.UniqueConstraint('name', 'match_type', name='RESOURCE_NAME_IDX'),
    )

    def __repr__(self):
        return f"<Resource id={self.id}, name={self.name}, match_type={self.match_type}>"

    @staticmethod
    def get_by_name_and_match_type(name, match_type):
        """根据 name 和 match_type 查询资源"""
        return Resource.query.filter_by(name=name, match_type=match_type).first()
    
    @staticmethod
    def get_resource_by_name_and_action(name, action):
        """根据 appID、action 和资源名称查找资源"""
        
        # 构建查询条件
       # 定义条件
        conditions = and_(
            Resource.action.in_([action, 'ALL']),
            or_(
                and_(
                    Resource.match_type == MatchType.EQUAL,
                    Resource.name == name
                ),
                and_(
                    Resource.match_type == MatchType.SUFFIX,
                    func.right(name, func.length(Resource.name)) == Resource.name
                ),
                and_(
                    Resource.match_type == MatchType.PREFIX,
                    func.substr(name, 1, func.length(Resource.name)) == Resource.name
                )
            ),
        )

        resource = (Resource.query
                    .filter(conditions)  # 应用条件
                    .order_by(Resource.priority.asc())  # 按优先级 ASC 排序
                    .first())  # 仅返回第一个匹配的结果
        return resource