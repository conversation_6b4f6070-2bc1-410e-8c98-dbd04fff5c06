from app.extension import db



class RolePermission(db.Model):
    __tablename__ = 'ROLE_PERMISSION'

    role_id = db.Column(db.String(100), db.<PERSON><PERSON>('ROLES.id'), primary_key=True, nullable=False, comment="角色ID")
    perm_id = db.Column(db.String(100), db.<PERSON><PERSON>('PERMISSIONS.id'), primary_key=True, nullable=False, comment="权限ID")

    # 定义关系
    # role = db.relationship("Role", back_populates="permissions", primaryjoin="RolePermission.role_id == Role.id")
    # permission = db.relationship("Permission", back_populates="roles", primaryjoin="RolePermission.perm_id == Permission.id")

    def __repr__(self):
        return f"<RolePermission role_id={self.role_id} perm_id={self.perm_id}>"

    def to_dict(self):
        """将模型实例转换为字典"""
        return {
            "role_id": self.role_id,
            "perm_id": self.perm_id,
            "permission": self.permission.to_dict(),
            "role": self.role.to_dict()
        }

    def save(self):
        """
        保存当前实例
        """
        db.session.add(self)
        db.session.commit()