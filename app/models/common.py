from flask_restx import fields
from app.extension import db
def generate_model_from_sqlalchemy(namespace, model, excluded_fields=None):
    """
    从 SQLAlchemy 模型动态生成 Flask-RESTx 的字段模型
    :param namespace: Flask-RESTx 的命名空间对象
    :param model: SQLAlchemy 的模型
    :param excluded_fields: 需要排除的字段列表
    :return: 动态生成的 Flask-RESTx 模型
    """
    if excluded_fields is None:
        excluded_fields = []

    model_fields = {}
    for column in model.__table__.columns:
        if column.name in excluded_fields:
            continue

        if isinstance(column.type, db.Integer):
            model_fields[column.name] = fields.Integer(description=column.comment)
        elif isinstance(column.type, db.String):
            model_fields[column.name] = fields.String(description=column.comment)
        elif isinstance(column.type, db.Boolean):
            model_fields[column.name] = fields.Boolean(description=column.comment)
        elif isinstance(column.type, db.Date):
            model_fields[column.name] = fields.String(description=column.comment)  # Date 转换为字符串
        elif isinstance(column.type, db.TIMESTAMP):
            model_fields[column.name] = fields.String(description=column.comment)  # TIMESTAMP 转换为字符串
        else:
            model_fields[column.name] = fields.Raw(description=column.comment)  # 默认处理

    return namespace.model(model.__tablename__, model_fields)
