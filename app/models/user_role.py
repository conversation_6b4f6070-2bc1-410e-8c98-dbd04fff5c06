from app.extension import db
# from app.models.role import Role
# from app.models.user import User

class UserRole(db.Model):
    __tablename__ = 'USER_ROLE'
    # __table_args__ = {"schema": "USER_CENTER"}  # 指定表的模式

    username = db.<PERSON>umn(db.String(100), db.<PERSON>('USERS.username'), primary_key=True, nullable=False, comment="用户名")
    role_id = db.Column(db.String(100), db.<PERSON><PERSON>('ROLES.id'), primary_key=True, nullable=False, comment="角色 ID")

    # 定义关系
    # user = db.relationship(
    #     "User",
    #     primaryjoin="UserRole.username == User.username",
    #     back_populates="roles"
    # )
    # role = db.relationship(
    #     "Role",
    #     primaryjoin="UserRole.role_id == Role.id",
    #     back_populates="users"
    # )

    def __repr__(self):
        return f"<UserRole username={self.username} role_id={self.role_id}>"

    def to_dict(self):
        """将模型实例转换为字典"""
        return {
            "username": self.username,
            "role_id": self.role_id
        }

    @staticmethod
    def get_user_roles(usernames: list):
        """获取用户的角色列表，以字典形式返回 {username: [role_id_list]}"""
        user_roles = UserRole.query.filter(UserRole.username.in_(usernames)).all()
        result = {}
        
        for user_role in user_roles:
            if user_role.username not in result:
                result[user_role.username] = []
            result[user_role.username].append(user_role.role_id)
        return result