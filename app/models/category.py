from datetime import datetime
from app.extension import db
from app.models.base_model import BaseModel

class Category(BaseModel):
    __tablename__ = 'CATEGORY'

    # id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment='资源分类id')
    # app_id = db.Column(db.String(64), nullable=False, comment='所属应用id')
    name = db.Column(db.String(128), comment='资源分类名称')
    # create_time = db.Column(db.BigInteger, nullable=False, comment='创建时间（Unix 时间戳）')
    # update_time = db.Column(db.BigInteger, nullable=False, comment='更新时间（Unix 时间戳）')
    # create_time = db.Column(db.TIMESTAMP, default=datetime.utcnow, nullable=True, comment="创建时间")
    # update_time = db.Column(db.TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=True, comment="更新时间")

    permissions = db.relationship('Permission', back_populates='category', primaryjoin='Category.id == Permission.category_id')
    def __repr__(self):
        return f'<Category {self.name}>'

    # def to_dict(self):
    #     """将模型转换为字典"""
    #     return {c.name: getattr(self, c.name) for c in self.__table__.columns}
