import json
import logging
from app.extension import db
from datetime import datetime

from app.models.base_model import BaseModel
from app.utils.common import encode_password

logger = logging.getLogger('flask_app')

user_role = db.Table('user_role',
    db.Column('USERNAME', db.String(100), db.<PERSON>('USER.username'), primary_key=True),
    db.Column('ROLE_ID', db.String(100), db.<PERSON>ey('ROLE.id'), primary_key=True),
    extend_existing=True
)

class User(BaseModel):
    __tablename__ = 'USERS'
    # id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='自动递增的主键')
    username = db.Column(db.String(50), unique=True, nullable=False, comment='用户名,长度限制为 50 个字符,不能为 NULL')
    nickname = db.Column(db.String(100), comment='昵称,长度限制为 100 个字符')
    email = db.Column(db.String(256), comment='电子邮件')
    tel = db.Column(db.String(64), comment='手机号码')
    password = db.Column(db.Text, comment='用户密码')
    app_ids = db.Column(db.String(64), comment='应用程序 ID 列表')
    manager = db.Column(db.Text, comment='管理员名称')
    status = db.Column(db.SmallInteger, comment='用户状态, 0 禁用 1 启用 ')
    auth_type = db.Column(db.SmallInteger, comment='认证类型 1: password, 2: LDAP')
    last_login = db.Column(db.Integer, comment='上次登录时间(Unix 时间戳)')
    profile = db.Column(db.Text, comment='用户个人资料(JSON 格式)')
    fullname = db.Column(db.String(64), nullable=False, comment='用户全名')
    is_disabled = db.Column(db.Boolean, default=False, comment='是否禁用')
    is_locked = db.Column(db.Boolean, default=False, comment='是否锁定')
    create_at = db.Column(db.TIMESTAMP, default=db.func.now(), comment='创建日期')
    update_at = db.Column(db.TIMESTAMP, default=db.func.now(), onupdate=db.func.now(), comment='更新日期')
    is_system = db.Column(db.Boolean, default=False, comment='是否为系统账号')
    is_public = db.Column(db.Boolean, default=False, comment='是否为公共账号')
    is_master = db.Column(db.Boolean, default=False, comment='是否为主账号')
    sex = db.Column(db.SmallInteger, comment='性别 1 男 2 女')
    birthday = db.Column(db.Date, comment='生日')
    dep_level = db.Column(db.String(64), comment='部门级别')
    dep_name = db.Column(db.String(64), comment='部门名称')
    dep_code = db.Column(db.String(64), comment='部门编码')
    com_level = db.Column(db.String(64), comment='公司级别')
    com_name = db.Column(db.String(64), comment='公司名称')
    com_code = db.Column(db.String(64), db.ForeignKey('ORGANIZATIONS.code'), comment='公司编码')
    job_name = db.Column(db.String(64), comment='职位名称')
    job_code = db.Column(db.String(64), comment='职位编码')
    office_phone = db.Column(db.String(20), comment='办公电话')
    identity_card = db.Column(db.String(18), comment='身份证号码')

    # 关联到 UserRole
    # roles = db.relationship("UserRole",  primaryjoin="User.username == UserRole.username", lazy="dynamic")
    roles = db.relationship("Role", secondary=user_role, back_populates='users', lazy='joined')
    organization = db.relationship("Organization", back_populates="users", primaryjoin="User.com_code == Organization.code", lazy="noload")

    def __repr__(self):
        return f'<User {self.username}>'

    def to_dict(self):
        """将模型转换为字典"""
        info = super().to_dict()
        if self.update_at:
            if isinstance(self.update_at, str):
                info['update_at'] = self.update_at
            else:
                info['update_at'] = self.update_at.strftime('%Y-%m-%d %H:%M:%S')

        if self.create_at:
            if isinstance(self.create_at, str):
                info['create_at'] = self.create_at
            else:
                info['create_at'] = self.create_at.strftime('%Y-%m-%d %H:%M:%S')
        if self.birthday:
            if isinstance(self.birthday, str):
                info['birthday'] = self.birthday
            else:
                info['birthday'] = self.birthday.strftime('%Y-%m-%d')
        # info['permission_ids'] = self.get_permissions()
        # info['role_ids'] = [role.role_id for role in self.roles.all()]
        info['manager'] = self.manager if self.manager else 'none'
        info['code'] = self.username
        info['user_name'] = self.nickname
        info['roles'] = [role.to_dict() for role in self.roles]
        role_ids, permission_ids = self.get_role_permission_ids()
        info['role_ids'] = role_ids
        info['permission_ids'] = permission_ids
        exclude_fields = ['password', 'app_ids',  'auth_type', 'identity_card']
        for field in exclude_fields:
            # logger.debug(f"field:{field} {field in info}")
            if field in info:
                del info[field]
        return info


    @staticmethod
    def format_birthday(birthday):
        if not birthday:
            return None
        if isinstance(birthday, str):
            return birthday
        return birthday.strftime('%Y-%m-%d')


    def get_role_permission_ids(self):
        """获取用户的所有权限"""
        permissions = set()  # 使用集合来防止重复
        roles = set()
        for role in self.roles:
            roles.add(role.id)
            if role.permissions:
                for permission in role.permissions:
                    permissions.add(permission.id)  # 假设 Permission 有 `perm_id` 字段
        return list(roles), list(permissions)  # 转换为列表并返回

    @staticmethod
    def get_jwt_claims(user):
        """
        返回JWT中的payload的字段值
        """
        claims = {
            "id": user.id,
            "username": user.username,
            "user_name": user.nickname,
            "manager": user.manager,
            "user_code": user.username,
            "user_role": user.profile.get("role_ids", []) if user.profile else [],  # 默认角色为空
            "department_code": user.dep_code or "",
            "department_name": user.dep_name or "",
            "enterprise_code": user.com_code or "",
            "enterprise_name": user.com_name or "",
            "enterprise_level": int(user.com_level) if user.com_level else 0,  # 如果公司级别不存在,默认为0
        }
        return claims

    @staticmethod
    def get_user_by_username(username: str):
        user = User.query.filter_by(username=username).first()
        if not user:
            return None
        roles = [role.role_id for role in user.roles.all()]
        # user_info = user.to_dict()
        user.role_ids = roles
        return user

    @classmethod
    def json_to_user(cls, user_data:dict):
        user = cls()
        for key, value in user_data.items():
            if hasattr(User, key) and key not in ['roles', 'id']:
                setattr(user, key, value)
        return user

    @staticmethod
    def save_data(data:dict):
        """保存用户数据,支持新建和更新
        Args:
            data (dict): 用户数据字典
        Returns:
            tuple: (bool, dict/str) 成功返回(True, user_dict),失败返回(False, error_message)
        """
        try:
            # 检查必填字段
            username = data.get('username')
            if not username:
                logger.error("Username not found in data")
                return False, f"Username not found in data: {data}"
            
            fullname = data.get('fullname')
            if not fullname:
                logger.error(f"Fullname is required but not found for username: {username}")
                return False, f"Fullname is required but not found in data: {data}"

            # 查找或创建用户
            user = User.query.filter_by(username=username).first()
            if not user:
                user = User()
                user.username = username
                # 获取当前最大ID并加1
                # max_id = db.session.query(db.func.max(User.id)).scalar() or 0
                # user.id = max_id + 1
                # user.username = username
                logger.info(f"Creating new user with username: {username}")
            else:
                logger.info(f"Updating existing user with username: {username}")

            # 设置用户属性
            user.fullname = fullname
            user.nickname = fullname  # 默认昵称为全名
            user.is_disabled = data.get('isDisabled', False)
            user.is_locked = data.get('isLocked', False)
            user.is_system = data.get('isSystem', False)
            user.is_public = data.get('isPublic', False)
            user.is_master = data.get('isMaster', False)

            # 处理日期时间
            create_at_str = data.get('createAt')
            if create_at_str:
                try:
                    user.create_at = datetime.strptime(create_at_str, '%Y-%m-%d %H:%M:%S.%f')
                except ValueError:
                    try:
                        user.create_at = datetime.strptime(create_at_str, '%Y-%m-%d %H:%M:%S')
                    except ValueError as e:
                        logger.warning(f"Could not parse createAt datetime string: {create_at_str}, error: {e}")

            user.update_at = datetime.now()

            # 处理其他字段
            if data.get('sex'):
                user.sex = int(data.get('sex'))

            user.dep_level = data.get('depLevel')
            user.dep_name = data.get('depName')
            user.dep_code = data.get('depCode')
            user.com_level = data.get('comLevel')
            user.com_name = data.get('comName')
            user.com_code = data.get('comCode')
            user.job_name = data.get('jobName')
            user.job_code = data.get('jobCode')
            user.office_phone = data.get('officePhone')
            user.identity_card = data.get('identityCard')

            # 处理手机号
            mobile_str = data.get('mobile')
            if mobile_str:
                mobiles = list(set(mobile_str.split(',')))  # 去重
                user.tel = ','.join(mobiles) if mobiles else None

            user.email = data.get('email')

            # 处理用户状态
            user_status_str = data.get('userStatus')
            user.status = 1 if user_status_str == '1' else 0

            # 处理生日
            birthday_str = data.get('birthday')
            if birthday_str:
                try:
                    user.birthday = datetime.strptime(birthday_str, '%Y-%m-%d').date()
                except ValueError as e:
                    logger.warning(f"Could not parse birthday date string: {birthday_str}, error: {e}")

            user.manager = data.get('manager', 'none')

            # 设置默认密码(仅针对新用户)
            if not user.password:
                user.password = encode_password('123456')

            # 保存到数据库
            db.session.add(user)
            db.session.commit()
            logger.info(f"Successfully saved user data for username: {username}")
            return True, {'code': user.username, **user.to_dict()}

        except Exception as e:
            db.session.rollback()
            error_msg = f"Error saving user data for username {username}: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def is_valid(self):
        """判断用户是否有效"""
        return self.status == 1

    @classmethod
    def init_system_users(cls):
        """初始化系统用户（root和admin）"""
        from app.config import Config
        from app.extension import db
        # import logging
        # logger = logging.getLogger('flask_app')

        system_users = [
            {
                'username': 'root',
                'fullname': 'Root User',
                'is_system': True,
                'status': 1,
                'manager': 'super'
            },
            {
                'username': 'admin',
                'fullname': 'Admin User',
                'is_system': True,
                'status': 1,
                'manager': 'admin'
            }
        ]

        try:
            for user_data in system_users:
                username = user_data['username']
                user = cls.query.filter_by(username=username).first()

                if not user:
                    # 创建新用户
                    user = cls()
                    for key, value in user_data.items():
                        setattr(user, key, value)
                    user.nickname = user_data['fullname']

                    # 设置初始密码
                    initial_password = Config.MANAGE_PASSWORD if Config.MANAGE_PASSWORD else '123456'
                    user.password = encode_password(initial_password)

                    db.session.add(user)
                    logger.info(f"Created new system user: {username}")
                else:
                    # 用户已存在，更新系统用户属性
                    for key, value in user_data.items():
                        setattr(user, key, value)
                    user.nickname = user_data['fullname']

                    # 只有在MANAGE_PASSWORD有值且不是默认值时才更新密码
                    if Config.MANAGE_PASSWORD and Config.MANAGE_PASSWORD != '123456':
                        logger.info(f"password is {Config.MANAGE_PASSWORD}")
                        user.password = encode_password(Config.MANAGE_PASSWORD)
                        logger.info(f"Updated password for existing user: {username}")
                    else:
                        logger.info(f"Skipped password update for existing user: {username}")

            db.session.commit()
            logger.info("System users initialization completed successfully")
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error initializing system users: {str(e)}")
            raise
