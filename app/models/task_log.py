# models/task_log.py

from app.extension import db
from app.models.base_model import BaseModel  # 导入 BaseModel
from sqlalchemy import Text
from sqlalchemy.orm import relationship
from datetime import datetime, timedelta, timezone

# 定义北京时间 (UTC+8)
BEIJING_TIMEZONE = timezone(timedelta(hours=8))

class TaskLog(BaseModel):  # 继承自 BaseModel
    __tablename__ = 'task_logs'

    # 任务名称 pullTask:增量, syncTask:全量
    task_name = db.Column(db.String(50), nullable=False, comment="任务名称 pullTask:增量, syncTask:全量")
    start_time = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(BEIJING_TIMEZONE), comment="任务开始时间")
    end_time = db.Column(db.DateTime, nullable=True, comment="任务结束时间")
    status = db.Column(db.String(20), nullable=True, default='running', comment="任务状态: 'success' or 'failed', 'running'")
    message = db.Column(Text, nullable=True, comment="任务信息")
    total_records = db.Column(db.Integer, nullable=True, comment="更新或新增的数据总条目")
    # 登录成功后返回的token_id
    token_id = db.Column(db.String(100), nullable=True, comment="token_id")
    
    # 关联关系 - 一个TaskLog可以有多个TaskFailure
    failures = relationship("TaskFailure", back_populates="task_log", cascade="all, delete-orphan")

    def __init__(self, task_name, message='', total_records=None, token_id=None):
        super().__init__()  # 调用父类的初始化方法
        self.task_name = task_name
        self.message = message
        self.total_records = total_records
        self.start_time = datetime.now(BEIJING_TIMEZONE)
        self.token_id = token_id
