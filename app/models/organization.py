import json
import logging
from typing import List, Dict, Optional, Tuple, Set
from app.extension import db
from sqlalchemy.orm import relationship
from sqlalchemy import and_, or_
from app.models.base_model import BaseModel
from app.models.user import User
from app.utils.redis_utils import OrganizationRedisUtils

logger = logging.getLogger('flask_app')


class OrgType:
    """组织类型常量"""
    ENTERPRISE = "enterprise"
    DEPARTMENT = "department"
    USER = "user"


class OrgFilter:
    """组织过滤条件常量"""
    RETIRED_NAME = "离退休"
    ACTIVE_STATUS = "1"
    DEFAULT_ROOT_LEVEL = "01"


class Organization(BaseModel):
    __tablename__ = 'ORGANIZATIONS'
    # id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment="主键")
    _parent = db.Column(db.String(128), db.ForeignKey('ORGANIZATIONS.code'), comment="父机构")
    code = db.Column(db.String(128), unique=True, nullable=False, comment="代码")
    name = db.Column(db.String(128), nullable=False, comment="名称")
    fullname = db.Column(db.String(128), comment="机构全名")
    description = db.Column(db.String(512), comment="描述")
    is_disabled = db.Column(db.Boolean, default=False, comment="禁用")
    _organization = db.Column(db.String(128), comment="机构")
    is_virtual_org = db.Column(db.Boolean, default=False, comment="是否虚拟组织")
    com_level = db.Column(db.String(32), comment="隶属公司层级")
    com_name = db.Column(db.String(256), comment="隶属公司名称")
    com_code = db.Column(db.String(64), comment="隶属公司编码")
    org_status = db.Column(db.String(32), comment="机构状态")
    org_level = db.Column(db.String(32), comment="机构层级")
    parent_org_code = db.Column(db.String(64), db.ForeignKey('ORGANIZATIONS.code'), comment="父级机构编号")

    # 定义关系
    users = relationship("User", back_populates="organization", primaryjoin="Organization.code == User.com_code",
                         lazy='dynamic')
    children = relationship("Organization", backref=db.backref("parent", remote_side=[code]), foreign_keys=[parent_org_code],
                            lazy='dynamic')

    @staticmethod
    def save_data(model_data: dict) -> Tuple[bool, dict]:
        """保存组织数据"""
        try:
            # 预处理数据
            processed_data = Organization._preprocess_data(model_data)
            
            # 查找或创建组织
            org = Organization.query.filter_by(code=processed_data["code"]).first()
            if not org:
                org = Organization()

            # 批量设置属性
            Organization._set_org_attributes(org, processed_data)
            
            db.session.add(org)
            db.session.commit()
            
            logger.info(f"{org.name} saved successful")
            return True, {'code': org.code, **org.to_dict()}
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error saving organization {model_data.get('code', 'unknown')}: {e}")
            return False, str(e)

    @staticmethod
    def _preprocess_data(data: dict) -> dict:
        """预处理组织数据"""
        processed = data.copy()
        
        # 处理父级关系
        if processed.get("_parent") == "None":
            processed["_parent"] = None
        elif (processed.get("_parent") != processed.get("parentOrgCode") and 
              processed.get("parentOrgCode") != "50002000"):
            processed["_parent"] = processed.get("parentOrgCode")
            
        return processed

    @staticmethod
    def _set_org_attributes(org: 'Organization', data: dict) -> None:
        """批量设置组织属性"""
        attribute_mapping = {
            '_parent': '_parent',
            'code': 'code',
            'name': 'name',
            'fullname': 'fullname',
            'description': 'description',
            'isDisabled': 'is_disabled',
            '_organization': '_organization',
            'isVirtualOrg': 'is_virtual_org',
            'comLevel': 'com_level',
            'comName': 'com_name',
            'comCode': 'com_code',
            'orgStatus': 'org_status',
            'orgLevel': 'org_level',
            'parentOrgCode': 'parent_org_code'
        }
        
        for data_key, attr_name in attribute_mapping.items():
            if data_key in data:
                setattr(org, attr_name, data[data_key])

    @classmethod
    def get_base_query(cls):
        """获取基础查询条件"""
        return cls.query.filter(
            cls.fullname.isnot(None),
            cls.name != OrgFilter.RETIRED_NAME,
            cls.is_disabled == False
        )

    @classmethod
    def get_tree_data(cls) -> List[dict]:
        """获取缓存的树形数据，如果没有则构建"""
        cached_data = OrganizationRedisUtils.get_tree_data()
        if cached_data:
            return json.loads(cached_data)
        return cls.build_tree()

    @classmethod
    def build_tree(cls) -> List[dict]:
        """构建树形结构并缓存"""
        orgs = cls.get_base_query().all()
        tree_data = cls._build_tree([org.to_dict() for org in orgs])
        OrganizationRedisUtils.set_tree_data(json.dumps(tree_data))
        return tree_data

    @classmethod
    def get_tree(cls, **kwargs) -> List[dict]:
        """获取树形结构数据 - 简化版本"""
        # 确保树数据已构建
        cls.build_tree()
        
        # 获取查询参数
        filters = cls._extract_tree_filters(**kwargs)
        
        # 构建查询
        query = cls._build_tree_query(filters)
        organizations = query.all()
        
        # 如果没有特殊要求，直接返回简单树结构
        if not cls._needs_complex_tree(filters):
            return cls._build_tree([org.to_dict() for org in organizations])
        
        # 构建复杂树结构（包含用户等）
        return cls._build_complex_tree(organizations, filters)

    @staticmethod
    def _extract_tree_filters(**kwargs) -> dict:
        """提取树形查询的过滤条件"""
        return {
            'com_code': kwargs.get("com_code"),
            'com_level': kwargs.get("com_level", OrgFilter.DEFAULT_ROOT_LEVEL if not any([
                kwargs.get("com_code"), kwargs.get("com_level"), kwargs.get("key")
            ]) else None),
            'key': kwargs.get("key"),
            'include_departments': kwargs.get("include_departments", False),
            'include_enterprises': kwargs.get("include_enterprises", False),
            'include_users': kwargs.get("include_users", False),
            'include_virtual_org': kwargs.get("include_virtual_org", True),
        }

    @classmethod
    def _build_tree_query(cls, filters: dict):
        """构建树形查询"""
        query = cls.get_base_query()
        
        if filters['com_code']:
            query = query.filter(cls.code == filters['com_code'])
        if filters['com_level']:
            query = query.filter(cls.com_level == filters['com_level'])
        if filters['key']:
            query = query.filter(or_(
                cls.name.like(f"%{filters['key']}%"),
                cls.fullname.like(f"%{filters['key']}%")
            ))
        if not filters['include_virtual_org']:
            query = query.filter(cls.is_virtual_org == '0')
            
        return query

    @staticmethod
    def _needs_complex_tree(filters: dict) -> bool:
        """判断是否需要构建复杂树结构"""
        return any([
            filters['include_departments'],
            filters['include_enterprises'],
            filters['include_users']
        ])

    @classmethod
    def _build_complex_tree(cls, organizations: List['Organization'], filters: dict) -> List[dict]:
        """构建包含部门、企业、用户的复杂树结构"""
        org_codes = [org.code for org in organizations]
        all_orgs = cls._get_all_organizations_data()
        
        node_type = cls._determine_node_type(filters)
        tree_data, dept_codes = cls._build_tree_with_children(all_orgs, org_codes, node_type, filters)
        
        # 添加用户数据
        if filters['include_users']:
            cls._add_users_to_tree(tree_data, dept_codes)

        return tree_data

    @staticmethod
    def _determine_node_type(filters: dict) -> str:
        """确定节点类型"""
        if filters['include_departments'] and filters['include_enterprises']:
            return 'all'
        elif filters['include_departments']:
            return OrgType.DEPARTMENT
        elif filters['include_enterprises']:
            return OrgType.ENTERPRISE
        elif filters['include_users']:
            return OrgType.DEPARTMENT
        return 'all'

    @classmethod
    def _build_tree_with_children(cls, all_orgs: List[dict], parent_codes: List[str], 
                                 node_type: str, filters: dict) -> Tuple[List[dict], List[str]]:
        """构建包含子节点的树结构"""
        dept_codes = []
        tree_data = []
        
        # 构建组织映射
        org_map = {org["code"]: org for org in all_orgs}
        
        def build_children(parent_code: str) -> List[dict]:
            children = []
            for org in all_orgs:
                if (cls._should_include_org(org, parent_code, node_type, filters) and
                    org.get("parent_org_code") == parent_code):
                    
                    if org.get("type") == OrgType.DEPARTMENT:
                        dept_codes.append(org["code"])
                    
                    org_copy = org.copy()
                    org_copy['children'] = build_children(org["code"])
                    children.append(org_copy)
            return children
        
        # 构建树
        for org in all_orgs:
            if org.get('code') in parent_codes:
                node = org.copy()
                node['children'] = build_children(org["code"])
                tree_data.append(node)
        
        return tree_data, dept_codes

    @staticmethod
    def _should_include_org(org: dict, parent_code: str, node_type: str, filters: dict) -> bool:
        """判断是否应该包含该组织"""
        if not filters['include_virtual_org'] and org.get("is_virtual_org"):
            return False
        return node_type in ['all', org.get("type")]

    @classmethod
    def _get_all_organizations_data(cls) -> List[dict]:
        """获取所有组织数据"""
        orgs = cls.get_all_data(return_tree=False)
        return [org.to_dict() if hasattr(org, 'to_dict') else org for org in orgs]

    @classmethod
    def get_all_children_codes(cls, com_code: str) -> List[str]:
        """获取指定代码的所有子级代码 - 简化版本"""
        all_orgs = cls._fetch_organizations_from_cache_or_db(False)
        
        result_codes = []
        org_map = {org["code"]: org for org in all_orgs}
        
        def collect_children(code: str):
            result_codes.append(code)
            for org in all_orgs:
                if org.get("parent_org_code") == code:
                    collect_children(org["code"])
        
        if com_code in org_map:
            collect_children(com_code)
        return result_codes

    @classmethod
    def _fetch_organizations_from_cache_or_db(cls, from_db: bool = False) -> List[dict]:
        """从缓存或数据库获取组织数据"""
        if not from_db:
            cached_orgs = OrganizationRedisUtils.get_all_organizations()
            if cached_orgs:
                try:
                    return json.loads(cached_orgs)
                except (json.JSONDecodeError, TypeError):
                    logger.warning("Failed to parse cached organizations, fetching from DB")
        
        # 从数据库获取并缓存
        orgs_data = [org.to_dict() for org in cls.get_all_data(return_tree=False)]
        OrganizationRedisUtils.set_all_organizations(json.dumps(orgs_data))
        return orgs_data

    @staticmethod
    def _build_tree(data: List[dict], parent_code: Optional[str] = None) -> List[dict]:
        """构建树形结构 - 优化版本"""
        if not data:
            return []
        
        # 构建快速查找映射
        org_map = {org["code"]: org for org in data}
        children_map = {}
        
        # 预处理子节点映射
        for org in data:
            parent = org.get("parent_org_code")
            if parent not in children_map:
                children_map[parent] = []
            children_map[parent].append(org)
        
        def create_node(org: dict) -> Optional[dict]:
            """创建树节点"""
            org_type = Organization._get_organization_type(org)
            if not org_type:
                return None
                
            return {
                "name": org.get("fullname") or org.get("name"),
                "code": org.get("code"),
                "type": org_type,
                "parent_org_code": org.get("parent_org_code"),
                "_parent": org.get("parent_org_code"),
                "level": org.get("org_level") or org.get("com_level"),
                "children": build_children(org.get("code")),
                "is_virtual_org": org.get("is_virtual_org", False)
            }
        
        def build_children(parent_code: str) -> List[dict]:
            """递归构建子节点"""
            return [
                node for child in children_map.get(parent_code, [])
                if (node := create_node(child)) is not None
            ]
        
        # 构建树
        if parent_code is not None:
            return build_children(parent_code)
        
        # 找到根节点
        root_orgs = children_map.get(None, [])
        # 添加那些父节点不在当前数据集中的节点作为根节点
        for org in data:
            parent = org.get("parent_org_code")
            if parent and parent not in org_map and org not in root_orgs:
                root_orgs.append(org)
            
        return [
            node for org in root_orgs
            if (node := create_node(org)) is not None
        ]

    @staticmethod
    def _add_users_to_tree(tree: List[dict], dept_codes: List[str]) -> None:
        """向树中添加用户数据 - 优化版本"""
        if not dept_codes:
            return
        
        # 批量查询用户
        users = User.get_query().filter(
            and_(User.dep_code.in_(dept_codes), User.status == 1)
        ).all()
        
        # 按部门分组用户
        users_by_dept = {}
        for user in users:
            dept_code = user.dep_code
            if dept_code not in users_by_dept:
                users_by_dept[dept_code] = []
            users_by_dept[dept_code].append({
                "name": user.nickname,
                "code": user.username,
                "birthday": User.format_birthday(user.birthday),
                "dep_name": user.dep_name,
                "com_code": user.com_code,
                "dep_code": user.dep_code,
                "com_name": user.com_name,
                "type": OrgType.USER
            })
        
        # 递归添加用户到部门节点
        def add_users_to_node(node: dict):
            if (node["type"] == OrgType.DEPARTMENT and 
                node["code"] in users_by_dept):
                node["children"].extend(users_by_dept[node["code"]])
            
            for child in node.get("children", []):
                add_users_to_node(child)

        for node in tree:
            add_users_to_node(node)

    @staticmethod
    def _get_organization_type(org: dict) -> Optional[str]:
        """获取组织类型 - 优化版本"""
        has_com_level = org.get("com_level")
        has_org_level = org.get("org_level")
        
        if has_com_level and not has_org_level:
            return OrgType.ENTERPRISE
        elif has_org_level:
            return OrgType.DEPARTMENT
        elif not has_org_level and not has_com_level and org.get("code") and not org.get("parent_org_code"):
            return OrgType.ENTERPRISE
        return None

    @classmethod
    def get_all_data(cls, return_tree: bool = True) -> List:
        """获取所有组织数据"""
        orgs = cls.get_base_query().order_by(cls.fullname.asc()).all()
        if return_tree:
            return cls._build_tree([org.to_dict() for org in orgs])
        return orgs

    def to_dict(self) -> dict:
        """转换为字典 - 优化版本，保留parent_org_code"""
        data = super().to_dict()
        
        # 移除不需要的字段，但保留parent_org_code
        fields_to_remove = ["_organization", "update_time", 
                           "is_disabled", "create_time", "description"]
        for field in fields_to_remove:
            data.pop(field, None)
        
        # 添加类型信息
        data['type'] = self._get_organization_type(data)
        
        # 确保parent_org_code字段存在
        if 'parent_org_code' not in data:
            data['parent_org_code'] = self.parent_org_code
            
        return data

    def __repr__(self) -> str:
        return f"<Organization code={self.code} name={self.name}>"

    @classmethod
    def get_children_tree(cls, parent_code: str, include_departments: bool = True, 
                         include_enterprises: bool = True) -> List[dict]:
        """获取子节点的树形结构 - 简化版本"""
        if not include_departments and not include_enterprises:
            return []
        
        tree_data = cls.get_tree_data()
        return cls._build_tree(tree_data, parent_code)

    @classmethod
    def get_children_codes(cls, parent_code: str) -> List[str]:
        """获取子节点的代码列表 - 简化版本"""
        all_data = cls.get_all_data(return_tree=True)
        
        def find_and_collect_codes(nodes: List[dict], target_code: str) -> List[str]:
            """查找目标节点并收集其所有子代码"""
            for node in nodes:
                if node["code"] == target_code:
                    return cls._collect_all_codes(node)
                # 递归查找子节点
                if node.get("children"):
                    result = find_and_collect_codes(node["children"], target_code)
                    if result:
                        return result
            return []
        
        return find_and_collect_codes(all_data, parent_code)

    @staticmethod
    def _collect_all_codes(node: dict) -> List[str]:
        """收集节点及其所有子节点的代码"""
        codes = [node["code"]]
        for child in node.get("children", []):
            codes.extend(Organization._collect_all_codes(child))
        return codes

    @classmethod
    def get_organizations_by_level(cls, com_level: str, **filters) -> List['Organization']:
        """根据级别获取组织列表"""
        query = cls.get_base_query().filter(cls.com_level == com_level)
        
        # 应用额外过滤条件
        if filters.get('search'):
            search_term = f"%{filters['search']}%"
            query = query.filter(or_(
                cls.name.like(search_term),
                cls.fullname.like(search_term)
            ))
        
        if filters.get('parent_code'):
            query = query.filter(cls.parent_org_code == filters['parent_code'])
            
        if filters.get('org_level_null'):
            query = query.filter(cls.org_level.is_(None))
        elif filters.get('org_level_not_null'):
            query = query.filter(cls.org_level.isnot(None))
            
        if not filters.get('include_virtual', True):
            query = query.filter(cls.is_virtual_org == False)
            
        return query.all()

    @classmethod
    def search_organizations(cls, search_term: str, **options) -> List[dict]:
        """搜索组织 - 统一搜索接口"""
        query = cls.get_base_query()
        
        if search_term:
            search_pattern = f"%{search_term}%"
            query = query.filter(or_(
                cls.name.like(search_pattern),
                cls.fullname.like(search_pattern),
                cls.code.like(search_pattern)
            ))
        
        # 应用选项过滤
        if options.get('com_levels'):
            levels = options['com_levels'].split(',') if isinstance(options['com_levels'], str) else options['com_levels']
            query = query.filter(cls.com_level.in_(levels))
            
        if options.get('parent_code'):
            query = query.filter(cls.parent_org_code == options['parent_code'])
            
        if not options.get('include_departments', True):
            query = query.filter(cls.org_level.is_(None))
            
        if not options.get('include_virtual', True):
            query = query.filter(cls.is_virtual_org == False)
        
        orgs = query.order_by(cls.fullname.asc(), cls.name.asc()).all()
        
        if options.get('as_tree', False):
            return cls._build_tree([org.to_dict() for org in orgs], options.get('parent_code'))
        
        return [org.to_dict() for org in orgs]

    @staticmethod
    def _build_tree_with_parent_org_code(data: List[dict], parent_code: Optional[str] = None) -> List[dict]:
        """使用parent_org_code构建树形结构"""
        if not data:
            return []
        
        # 构建快速查找映射
        org_map = {org["code"]: org for org in data}
        children_map = {}
        
        # 预处理子节点映射 - 使用parent_org_code而不是_parent
        for org in data:
            parent = org.get("parent_org_code")  # 使用parent_org_code
            if parent not in children_map:
                children_map[parent] = []
            children_map[parent].append(org)
        
        def create_node(org: dict) -> Optional[dict]:
            """创建树节点"""
            org_type = Organization._get_organization_type(org)
            if not org_type:
                return None
                
            return {
                "name": org.get("fullname") or org.get("name"),
                "code": org.get("code"),
                "type": org_type,
                "parent_org_code": org.get("parent_org_code"),  # 使用parent_org_code
                "level": org.get("org_level") or org.get("com_level"),
                "children": build_children(org.get("code")),
                "is_virtual_org": org.get("is_virtual_org", False)
            }
        
        def build_children(parent_code: str) -> List[dict]:
            """递归构建子节点"""
            return [
                node for child in children_map.get(parent_code, [])
                if (node := create_node(child)) is not None
            ]
        
        # 构建树
        if parent_code is not None and parent_code != '':
            # 如果指定了具体的 parent_code，构建该节点的子树
            return build_children(parent_code)
        
        # 当 parent_code 为 None 或空字符串时，从根节点开始构建树
        # 根节点是那些 parent_org_code 为 None 的节点
        root_orgs = children_map.get(None, [])
        
        # 还要添加那些 parent_org_code 不在当前数据集中的节点作为根节点
        # 这些可能是数据不完整的情况
        for org in data:
            parent = org.get("parent_org_code")
            if parent and parent not in org_map and org not in root_orgs:
                root_orgs.append(org)
        
        return [
            node for org in root_orgs
            if (node := create_node(org)) is not None
        ]

    @classmethod
    def search_organizations_with_parent_org_code(cls, search_term: str, **options) -> List[dict]:
        """使用parent_org_code的搜索组织方法"""
        query = cls.get_base_query()
        
        if search_term:
            search_pattern = f"%{search_term}%"
            query = query.filter(or_(
                cls.name.like(search_pattern),
                cls.fullname.like(search_pattern),
                cls.code.like(search_pattern)
            ))
        
        # 应用选项过滤
        if options.get('com_levels'):
            levels = options['com_levels'].split(',') if isinstance(options['com_levels'], str) else options['com_levels']
            query = query.filter(cls.com_level.in_(levels))
        
        # 处理 parent_code 参数
        parent_code = options.get('parent_code')
        if parent_code is not None and parent_code != '':
            # 如果指定了 parent_code，只查询该父节点下的数据
            query = query.filter(cls.parent_org_code == parent_code)
        elif options.get('as_tree', False):
            # 如果需要树形结构且没有指定 parent_code，获取所有数据来构建完整树
            # 这样可以构建以 parent_org_code=null 为根的完整树形结构
            pass  # 不添加过滤条件，获取所有符合条件的数据
        else:
            # 如果不需要树形结构且没有指定 parent_code，只返回根节点
            query = query.filter(cls.parent_org_code.is_(None))
            
        if not options.get('include_departments', True):
            query = query.filter(cls.org_level.is_(None))
            
        if not options.get('include_virtual', True):
            query = query.filter(cls.is_virtual_org == False)
        
        orgs = query.order_by(cls.fullname.asc(), cls.name.asc()).all()
        
        if options.get('as_tree', False):
            return cls._build_tree_with_parent_org_code([org.to_dict() for org in orgs], parent_code)
        
        return [org.to_dict() for org in orgs]

    @classmethod
    def get_all_tree_data_optimized(cls, **filters) -> List[dict]:
        """
        获取完整的树形数据并根据参数进行过滤
        按照记事本需求实现：先获取数据 -> 构建树形结构 -> 根据参数过滤
        """
        # 1. 先获取Organization模型的所有数据
        tree_data = cls._get_cached_or_build_tree_data()
        
        # 2. 根据查询参数对tree_data进行过滤
        filtered_data = cls._filter_tree_data(tree_data, **filters)
        
        return filtered_data
    
    @classmethod
    def _get_cached_or_build_tree_data(cls) -> List[dict]:
        """
        1. 先从redis查询，如果没有再从数据库获取
        2. tree_data没值或者json_load(tree_data)失败,从数据库查询
        3. 查询数据库时使用 Organization.get_base_query() 获取过滤某些条件后的query引用
        """
        # 1. 先从redis查询
        cached_data = OrganizationRedisUtils.get_tree_data()
        if cached_data:
            try:
                tree_data = json.loads(cached_data)
                if tree_data:  # 确保不是空数据
                    logger.debug("从Redis获取到树形数据")
                    return tree_data
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"Redis树形数据解析失败: {e}")
        
        # 2. 从数据库查询并构建树形结构
        logger.info("从数据库重新构建树形数据")
        tree_data = cls._build_and_cache_tree_data()
        return tree_data
    
    @classmethod
    def _build_and_cache_tree_data(cls) -> List[dict]:
        """
        从数据库构建树形数据并缓存到Redis
        """
        # 3. 查询数据库时使用 Organization.get_base_query() 获取过滤某些条件后的query引用
        organizations = cls.get_base_query().all()
        
        # 将数据转换成树形结构
        tree_data = cls._build_complete_tree([org.to_dict() for org in organizations])
        
        # 将树形数据保存到 redis
        try:
            OrganizationRedisUtils.set_tree_data(json.dumps(tree_data, ensure_ascii=False))
            logger.info(f"树形数据已缓存到Redis，根节点数量: {len(tree_data)}")
        except Exception as e:
            logger.error(f"缓存树形数据到Redis失败: {e}")
        
        return tree_data

    @staticmethod
    def _build_complete_tree(data: List[dict]) -> List[dict]:
        """
        通过 parent_org_code的关系组织成树形结构，同时格式化数据
        """
        if not data:
            return []
        
        # 构建快速查找映射
        org_map = {org["code"]: org for org in data}
        children_map = {}
        
        # 预处理子节点映射 - 使用parent_org_code
        for org in data:
            parent = org.get("parent_org_code")
            if parent not in children_map:
                children_map[parent] = []
            children_map[parent].append(org)
        
        def create_formatted_node(org: dict) -> Optional[dict]:
            """
            格式化数据，字段如下：
            name, fullname, code, type, _parent, parent_org_code, level, children, is_virtual_org
            """
            org_type = Organization._get_organization_type(org)
            if not org_type:
                return None
                
            return {
                "name": org.get("name"),
                "fullname": org.get("fullname"),
                "code": org.get("code"),
                "type": org_type,
                "parent_org_code": org.get("parent_org_code"),
                "_parent": org.get("parent_org_code"),
                "level": org.get("org_level") or org.get("com_level"),
                "children": build_children(org.get("code")),
                "is_virtual_org": org.get("is_virtual_org", False)
            }
        
        def build_children(parent_code: str) -> List[dict]:
            """递归构建子节点"""
            return [
                node for child in children_map.get(parent_code, [])
                if (node := create_formatted_node(child)) is not None
            ]
        
        # 找到根节点（parent_org_code为None的节点）
        root_orgs = children_map.get(None, [])
        logger.info(f"root_orgs: {root_orgs}")
        # 还要添加那些 parent_org_code 不在当前数据集中的节点作为根节点
        for org in data:
            parent = org.get("parent_org_code")
            if parent and parent not in org_map and org not in root_orgs:
                root_orgs.append(org)
        
        return [
            node for org in root_orgs
            if (node := create_formatted_node(org)) is not None
        ]
    
    @classmethod
    def _filter_tree_data(cls, tree_data: List[dict], **filters) -> List[dict]:
        """
        根据查询参数对tree_data进行过滤
        """
        search = filters.get('search', '')
        include_departments = filters.get('include_departments', True)
        include_enterprises = filters.get('include_enterprises', True)
        include_users = filters.get('include_users', False)
        com_level = filters.get('com_level', '')
        parent_code = filters.get('parent_code', '')
        include_virtual_org = filters.get('include_virtual_org', True)
        
        # 如果指定了 parent_code，只返回该节点的子树
        if parent_code:
            tree_data = cls._find_subtree_by_parent_code(tree_data, parent_code)
        
        # 递归过滤树形数据
        filtered_data = []
        for node in tree_data:
            filtered_node = cls._filter_node_recursive(
                node, search, include_departments, include_enterprises, 
                com_level, include_virtual_org
            )
            if filtered_node:
                filtered_data.append(filtered_node)
        
        # 如果需要包含用户数据，添加用户到部门节点
        if include_users:
            cls._add_users_to_filtered_tree(filtered_data)
        
        return filtered_data
    
    @classmethod
    def _add_users_to_filtered_tree(cls, tree_data: List[dict]) -> None:
        """
        为过滤后的树形数据添加用户信息
        """
        # 收集所有部门代码
        dept_codes = []
        
        def collect_dept_codes(nodes: List[dict]):
            for node in nodes:
                if node.get('type') == OrgType.DEPARTMENT:
                    dept_codes.append(node.get('code'))
                children = node.get('children', [])
                if children:
                    collect_dept_codes(children)
        
        collect_dept_codes(tree_data)
        
        # 使用现有的方法添加用户
        if dept_codes:
            cls._add_users_to_tree(tree_data, dept_codes)
    
    @classmethod
    def _find_subtree_by_parent_code(cls, tree_data: List[dict], parent_code: str) -> List[dict]:
        """
        根据 parent_code 查找子树
        """
        def find_node_recursive(nodes: List[dict], target_code: str) -> Optional[List[dict]]:
            for node in nodes:
                if node.get('code') == target_code:
                    return node.get('children', [])
                # 递归查找子节点
                children = node.get('children', [])
                if children:
                    result = find_node_recursive(children, target_code)
                    if result is not None:
                        return result
            return None

        result = find_node_recursive(tree_data, parent_code)
        return result if result is not None else []
    
    @classmethod
    def _filter_node_recursive(cls, node: dict, search: str, include_departments: bool, 
                              include_enterprises: bool, com_level: str, 
                              include_virtual_org: bool) -> Optional[dict]:
        """
        递归过滤节点
        注意实现：
        - 匹配时要遍历所有的children
        - search匹配时得返回匹配节点的父节点
        """
        # 过滤节点类型
        node_type = node.get('type')
        if not include_departments and node_type == 'department':
            return None
        if not include_enterprises and node_type == 'enterprise':
            return None
        
        # 过滤虚拟组织
        if not include_virtual_org and node.get('is_virtual_org', False):
            return None
        
        # 过滤公司级别
        if com_level and node.get('level') != com_level:
            # 如果指定了级别但不匹配，还要检查子节点是否有匹配的
            pass
        
        # 递归过滤子节点
        filtered_children = []
        for child in node.get('children', []):
            filtered_child = cls._filter_node_recursive(
                child, search, include_departments, include_enterprises, 
                com_level, include_virtual_org
            )
            if filtered_child:
                filtered_children.append(filtered_child)
        
        # 检查当前节点是否匹配搜索条件
        node_matches_search = cls._node_matches_search(node, search)
        
        # 检查当前节点是否匹配级别条件
        node_matches_level = not com_level or node.get('level') == com_level
        
        # 决定是否包含当前节点：
        # 1. 如果有搜索条件，节点或其子节点匹配搜索条件
        # 2. 如果有级别条件，节点匹配级别条件或有子节点匹配
        # 3. 如果没有搜索和级别条件，但有符合条件的子节点
        should_include = False
        
        if search:
            # search匹配时得返回匹配节点的父节点
            if node_matches_search or filtered_children:
                should_include = True
        elif com_level:
            if node_matches_level or filtered_children:
                should_include = True
        else:
            # 没有特殊条件，只要类型和虚拟组织匹配就包含
            should_include = True
        
        if should_include:
            result_node = node.copy()
            result_node['children'] = filtered_children
            return result_node
        
        return None
    
    @staticmethod
    def _node_matches_search(node: dict, search: str) -> bool:
        """
        检查节点是否匹配搜索条件
        search: 模糊匹配 code, name, fullname
        """
        if not search:
            return True
        
        search_lower = search.lower()
        
        # 检查 code, name, fullname 字段
        for field in ['code', 'name', 'fullname']:
            value = node.get(field, '')
            if value and search_lower in value.lower():
                return True
        
        return False

    @classmethod
    def get_enterprise_details(cls, com_code: str, include_enterprise: bool = True) -> List[dict]:
        """
        获取企业详情，按照层级关系：企业 -> 子企业 -> 部门 -> 用户
        
        Args:
            com_code: 企业代码
            include_enterprise: 是否包含下级企业
            
        Returns:
            List[dict]: 树形结构数据，格式与getTreeData类似
        """
        try:
            result = []
            all_dept_codes = []  # 收集所有部门代码，用于批量查询用户
            
            # 1. 获取直接子企业（如果需要）
            if include_enterprise:
                sub_enterprises = cls._get_sub_enterprises(com_code)
                for enterprise in sub_enterprises:
                    # 为每个子企业构建树结构（暂不包含用户）
                    enterprise_children, enterprise_dept_codes = cls._build_enterprise_tree_without_users(enterprise['code'])
                    enterprise['children'] = enterprise_children
                    all_dept_codes.extend(enterprise_dept_codes)
                    result.append(enterprise)
                
                # 处理特殊业务逻辑：获取特殊的三级企业
                special_enterprises = cls._get_special_level3_enterprises(com_code)
                for enterprise in special_enterprises:
                    # 为特殊企业也构建树结构（暂不包含用户）
                    enterprise_children, enterprise_dept_codes = cls._build_enterprise_tree_without_users(enterprise['code'])
                    enterprise['children'] = enterprise_children
                    all_dept_codes.extend(enterprise_dept_codes)
                    result.append(enterprise)
            
            # 2. 获取直接部门（企业直属部门）
            direct_departments = cls._get_direct_departments(com_code)
            for department in direct_departments:
                department['children'] = []  # 先设置为空，后面批量添加用户
                all_dept_codes.append(department['code'])
                result.append(department)
            
            # 3. 批量查询所有用户并添加到相应部门（使用 /getTreeData 的优化方式）
            if all_dept_codes:
                cls._add_users_to_tree_optimized(result, all_dept_codes)
            
            logger.info(f"企业详情查询完成，返回 {len(result)} 个根节点，涉及 {len(all_dept_codes)} 个部门")
            return result
            
        except Exception as e:
            logger.error(f"获取企业详情失败: {e}", exc_info=True)
            return []
    
    @classmethod
    def _get_sub_enterprises(cls, parent_code: str) -> List[dict]:
        """获取直接子企业（通过parent_org_code查询）"""
        try:
            # 查询parent_org_code为指定代码的企业
            sub_enterprises = cls.get_base_query().filter(
                cls.parent_org_code == parent_code,
                cls.org_level.is_(None),  # 确保是企业而不是部门
                cls.org_status == '1'
            ).all()
            
            enterprises = [
                {
                    'name': org.name,
                    'fullname': org.fullname,
                    'code': org.code,
                    'type': OrgType.ENTERPRISE,
                    'parent_org_code': org.parent_org_code,
                    '_parent': org.parent_org_code,
                    'level': org.com_level,
                    'children': [],
                    'is_virtual_org': org.is_virtual_org or False
                }
                for org in sub_enterprises
            ]
            
            logger.info(f"找到 {len(enterprises)} 个直接子企业")
            return enterprises
            
        except Exception as e:
            logger.error(f"获取直接子企业失败: {e}", exc_info=True)
            return []
    
    @classmethod
    def _get_direct_departments(cls, parent_code: str) -> List[dict]:
        """获取企业直属部门（通过parent_org_code查询）"""
        try:
            departments = cls.get_base_query().filter(
                cls.parent_org_code == parent_code,
                cls.org_level.isnot(None),  # 确保是部门
                cls.org_status == '1'
            ).all()
            
            dept_list = [
                {
                    'name': dept.name,
                    'fullname': dept.fullname,
                    'code': dept.code,
                    'type': OrgType.DEPARTMENT,
                    'parent_org_code': dept.parent_org_code,
                    '_parent': dept.parent_org_code,
                    'level': dept.org_level,
                    'children': [],
                    'is_virtual_org': dept.is_virtual_org or False
                }
                for dept in departments
            ]
            
            logger.info(f"找到 {len(dept_list)} 个直属部门")
            return dept_list
            
        except Exception as e:
            logger.error(f"获取直属部门失败: {e}", exc_info=True)
            return []
    
    @classmethod
    def _build_enterprise_tree_without_users(cls, enterprise_code: str) -> Tuple[List[dict], List[str]]:
        """为企业构建树结构（不包含用户），返回部门节点和部门代码列表"""
        try:
            result = []
            dept_codes = []
            
            # 获取企业下的所有部门（包括通过com_code和parent_org_code两种方式）
            departments = cls.get_base_query().filter(
                or_(
                    cls.com_code == enterprise_code,  # 通过com_code隶属的部门
                    cls.parent_org_code == enterprise_code  # 通过parent_org_code隶属的部门
                ),
                cls.org_level.isnot(None),  # 确保是部门
                cls.org_status == '1'
            ).all()
            
            for dept in departments:
                department_node = {
                    'name': dept.name,
                    'fullname': dept.fullname,
                    'code': dept.code,
                    'type': OrgType.DEPARTMENT,
                    'parent_org_code': dept.parent_org_code,
                    '_parent': dept.parent_org_code,
                    'level': dept.org_level,
                    'children': [],  # 不包含用户，后面批量添加
                    'is_virtual_org': dept.is_virtual_org or False
                }
                result.append(department_node)
                dept_codes.append(dept.code)
            
            logger.info(f"为企业 {enterprise_code} 构建了 {len(result)} 个部门树节点")
            return result, dept_codes
            
        except Exception as e:
            logger.error(f"为企业构建树结构失败: {e}", exc_info=True)
            return [], []
    
    @classmethod
    def _add_users_to_tree_optimized(cls, tree: List[dict], dept_codes: List[str]) -> None:
        """批量查询用户并添加到树中 - 优化版本（参考/getTreeData）"""
        if not dept_codes:
            return
        
        try:
            from app.models.user import User
            
            # 批量查询所有部门的用户
            users = User.query.filter(
                User.dep_code.in_(dept_codes),
                User.status == 1  # 只获取有效用户
            ).all()
            
            # 按部门分组用户
            users_by_dept = {}
            for user in users:
                dept_code = user.dep_code
                if dept_code not in users_by_dept:
                    users_by_dept[dept_code] = []
                users_by_dept[dept_code].append({
                    'name': user.nickname or user.username,
                    'fullname': user.nickname or user.username,
                    'code': user.username,
                    'type': OrgType.USER,
                    'parent_org_code': user.dep_code,
                    '_parent': user.dep_code,
                    'level': None,
                    'children': [],
                    'is_virtual_org': False,
                    # 用户特有字段
                    'username': user.username,
                    'nickname': user.nickname,
                    'dep_code': user.dep_code,
                    'dep_name': user.dep_name,
                    'com_code': user.com_code,
                    'com_name': user.com_name,
                    'user_name': user.fullname,
                    'birthday': User.format_birthday(user.birthday) if hasattr(User, 'format_birthday') else None
                })
            
            # 递归添加用户到部门节点
            def add_users_to_node(node: dict):
                if (node.get("type") == OrgType.DEPARTMENT and 
                    node.get("code") in users_by_dept):
                    node.setdefault("children", []).extend(users_by_dept[node["code"]])
                
                for child in node.get("children", []):
                    add_users_to_node(child)

            for node in tree:
                add_users_to_node(node)
            
            total_users = sum(len(users) for users in users_by_dept.values())
            logger.info(f"批量查询完成，为 {len(dept_codes)} 个部门添加了 {total_users} 个用户")
            
        except Exception as e:
            logger.error(f"批量添加用户失败: {e}", exc_info=True)
    
    @classmethod
    def _get_special_level3_enterprises(cls, com_code: str) -> List[dict]:
        """
        获取特殊情况下的三级企业：
        1. 检查当前节点是否为 com_level=02
        2. 检查父节点是否为 is_virtual_org=true
        3. 如果满足条件，获取父节点下所有 com_level=03 的企业
        返回格式与getTreeData一致的节点格式
        """
        try:
            # 查找当前企业节点
            current_org = cls.query.filter_by(code=com_code).first()
            
            if not current_org:
                logger.warning(f"未找到企业: {com_code}")
                return []
            
            logger.info(f"当前企业: code={current_org.code}, com_level={current_org.com_level}, parent_org_code={current_org.parent_org_code}")
            
            # 检查当前节点条件：com_level=02
            if current_org.com_level != '02':
                logger.info(f"当前企业不是二级企业: com_level={current_org.com_level}")
                return []
            
            # 检查是否有父节点
            if not current_org.parent_org_code:
                logger.info("当前企业没有父节点")
                return []
            
            # 查找父节点
            parent_org = cls.query.filter_by(code=current_org.parent_org_code).first()
            
            if not parent_org:
                logger.warning(f"未找到父节点: {current_org.parent_org_code}")
                return []
            
            logger.info(f"父节点: code={parent_org.code}, is_virtual_org={parent_org.is_virtual_org}")
            
            # 检查父节点条件：is_virtual_org=true
            if not parent_org.is_virtual_org:
                logger.info(f"父节点不是虚拟组织: is_virtual_org={parent_org.is_virtual_org}")
                return []
            
            # 复用现有的查询方法获取三级企业
            level3_enterprises = cls.get_organizations_by_level(
                com_level='03',
                parent_code=parent_org.code,
                org_level_null=True,
                include_virtual=False
            )
            
            # 格式化为与getTreeData一致的格式
            enterprises = [
                {
                    'name': org.name,
                    'fullname': org.fullname,
                    'code': org.code,
                    'type': OrgType.ENTERPRISE,
                    'parent_org_code': org.parent_org_code,
                    '_parent': org.parent_org_code,
                    'level': org.com_level,
                    'children': [],
                    'is_virtual_org': org.is_virtual_org or False
                }
                for org in level3_enterprises
            ]
            
            logger.info(f"找到 {len(enterprises)} 个特殊三级企业")
            return enterprises
            
        except Exception as e:
            logger.error(f"获取特殊三级企业失败: {e}", exc_info=True)
            return []
    
