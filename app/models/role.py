from datetime import datetime
from app.extension import db
from app.models.base_model import BaseModel

role_permission = db.Table('role_permission',
    db.Column('ROLE_ID', db.String(100), db.<PERSON><PERSON><PERSON>('ROLE.id'), primary_key=True),
    db.<PERSON>umn('PERM_ID', db.String(100), db.<PERSON>('PERMISSION.id'), primary_key=True),
    extend_existing=True
)

user_role = db.Table('user_role',
    db.<PERSON>umn('USERNAME', db.String(100), db.<PERSON><PERSON>('USERS.username'), primary_key=True),
    db.Column('ROLE_ID', db.String(100), db.<PERSON>('ROLES.id'), primary_key=True),
    extend_existing=True
)

class Role(BaseModel):
    __tablename__ = 'ROLES'
    name = db.Column(db.String(100), nullable=True, comment="角色名称")
    description = db.Column(db.String(100), nullable=True, comment="角色描述")
    
    # 关联到 UserRole
    # users = db.relationship("UserRole", back_populates="role", primaryjoin="UserRole.role_id == Role.id")
    users = db.relationship("User", secondary=user_role, back_populates='roles')

    permissions = db.relationship("Permission", secondary=role_permission, back_populates="roles", lazy='joined')

    
    def __repr__(self):
        return f"<Role id={self.id} name={self.name}>"

    def to_dict(self):
        """将模型实例转换为字典"""
        info = super().to_dict()
        info['permissions'] = [permission.to_dict() for permission in self.permissions]
        # info['perm_ids'] = self.get_perm_ids()
        return info

    def get_perm_ids(self):
        return [permission.perm_id for permission in self.permissions]