from app.extension import db
from app.models.base_model import BaseModel
from sqlalchemy import Text, ForeignKey
from sqlalchemy.orm import relationship


class TaskFailure(BaseModel):
    """任务失败记录模型，记录任务执行过程中的失败详情"""
    __tablename__ = 'task_failures'

    # 外键关联到task_logs表
    task_log_id = db.Column(db.String(64), ForeignKey('task_logs.id'), nullable=False, comment="关联的任务日志ID")
    
    # 失败记录的详细信息
    record_id = db.Column(db.String(64), nullable=True, comment="失败记录的ID")
    failure_reason = db.Column(Text, nullable=False, comment="失败原因")
    failure_detail = db.Column(Text, nullable=True, comment="失败详细信息")
    # 数据类型: user, organization
    data_type = db.Column(db.String(50), nullable=True, comment="数据类型")
    # 尝试次数
    try_count = db.Column(db.Integer, nullable=True, default=0, comment="尝试次数")
    # 失败时的行数据，JSON格式
    row_data = db.Column(Text, nullable=True, comment="失败时的行数据，JSON格式")
    # 结果: success, failed
    result = db.Column(db.String(50), nullable=True, comment="结果")
    # 关联关系
    task_log = relationship("TaskLog", back_populates="failures")

    def __init__(self, task_log_id, failure_reason, record_id=None, failure_detail=None, 
                 data_type=None, row_data=None, try_count=1, result=None):
        super().__init__()
        self.task_log_id = task_log_id
        self.record_id = record_id
        self.failure_reason = failure_reason
        self.failure_detail = failure_detail
        self.data_type = data_type
        self.row_data = row_data
        self.try_count = try_count
        self.result = result 