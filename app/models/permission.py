from app.extension import db
from datetime import datetime

from app.models.base_model import BaseModel


role_permission = db.Table('role_permission',
    db.Column('ROLE_ID', db.String(100), db.<PERSON><PERSON><PERSON>('ROLES.id'), primary_key=True),
    db.Column('PERM_ID', db.String(100), db.<PERSON>('PERMISSIONS.id'), primary_key=True),
    extend_existing=True
)

class Permission(BaseModel):
    __tablename__ = 'PERMISSIONS'
    __table_args__ = {"schema": "USER_CENTER"}  # 指定表的模式

    # id = db.Column(db.String(100), primary_key=True, nullable=False, comment="权限ID")
    # app_id = db.Column(db.String(100), nullable=True, comment="应用ID")
    name = db.Column(db.String(100), nullable=False, comment="权限名称")
    description = db.Column(db.String(100), nullable=True, comment="权限描述")
    category_id = db.Column(db.String(64), db.<PERSON><PERSON>('CATEGORY.id'), nullable=True,  comment="类别ID")
    # create_time = db.Column(db.TIMESTAMP, default=datetime.utcnow, nullable=True, comment="创建时间")
    # update_time = db.Column(db.TIMESTAMP, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=True, comment="更新时间")

    
    __table_args__ = (
        db.UniqueConstraint('id', name='PERMISSION_ID_IDX'),  # 创建唯一索引
        db.Index('PERMISSION_NAME_IDX', 'name'),  # 创建普通索引
    )

    # roles = db.relationship(
    #     "RolePermission",
    #     back_populates="permission",
    #     primaryjoin="RolePermission.perm_id == Permission.id",
    #     lazy="dynamic"
    # )
    roles = db.relationship("Role", secondary=role_permission, back_populates="permissions")

    category = db.relationship("Category", back_populates="permissions", primaryjoin="Permission.category_id == Category.id", lazy='joined')

    resources = db.relationship('Resource', back_populates='permission', primaryjoin='Resource.perm_id == Permission.id')
    def __repr__(self):
        return f"<Permission id={self.id}, name={self.name}>"

    def to_dict(self):
        """将模型实例转换为字典"""
        info = super().to_dict()
        info['category'] = self.category.to_dict() if self.category else None
        return info

    @staticmethod
    def get_by_id(permission_id):
        """根据ID查询权限"""
        return Permission.query.filter_by(id=permission_id).first()

    @staticmethod
    def get_by_name(name):
        """根据名称查询权限"""
        return Permission.query.filter_by(name=name).first()

    # def save(self):
    #     """保存当前实例"""
    #     db.session.add(self)
    #     db.session.commit()

    # def update(self, fields: dict):
    #     """更新当前实例的字段"""
    #     for key, value in fields.items():
    #         if hasattr(self, key):
    #             setattr(self, key, value)
    #     self.update_time = datetime.utcnow()
    #     db.session.commit()

    # def delete(self):
    #     """删除当前实例"""
    #     db.session.delete(self)
    #     db.session.commit()
