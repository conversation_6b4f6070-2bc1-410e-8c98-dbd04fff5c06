from app.extension import db
from app.models.base_model import BaseModel
class RoleAssign(BaseModel):
    __tablename__ = 'ROLE_ASSIGN'
    module = db.Column(db.String(64), nullable=False, comment="模块, assessment:企业考核，certification:产品认证, surveillance:质量监督, inspection:监督抽查")
    com_name = db.Column(db.String(100), nullable=False, comment="公司名称")
    com_code = db.Column(db.String(100), nullable=False, comment="公司编码")
    user_code = db.Column(db.String(100), nullable=True, comment="用户名")
    user_name = db.Column(db.String(100), nullable=True, comment="全名")
    is_leader = db.Column(db.<PERSON><PERSON><PERSON>, default=True, comment="是否领导")
    is_principal = db.Column(db.<PERSON>, default=True, comment="是否负责人")
    is_supervisor = db.Column(db.<PERSON><PERSON><PERSON>, default=True, comment="是否主管")
    is_admin = db.Column(db.<PERSON>, default=True, comment="是否管理员")