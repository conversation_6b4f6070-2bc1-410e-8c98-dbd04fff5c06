import uuid
from datetime import datetime, timedelta, timezone
from app.extension import db
# 定义北京时间 (UTC+8)
BEIJING_TIMEZONE = timezone(timedelta(hours=8))


class BaseModel(db.Model):
    __abstract__ = True  # 声明为抽象类，不会映射为表

    id = db.Column(db.String(64), primary_key=True, default=lambda: str(uuid.uuid4()), comment="主键UUID")
    create_time = db.Column(db.TIMESTAMP, default=lambda: datetime.now(BEIJING_TIMEZONE), nullable=False, comment="创建时间")
    update_time = db.Column(db.TIMESTAMP, default=lambda: datetime.now(BEIJING_TIMEZONE), onupdate=lambda: datetime.now(BEIJING_TIMEZONE), nullable=False, comment="更新时间")
    deleted_time = db.Column(db.TIMESTAMP, nullable=True, comment="删除时间")  # 删除时间字段，None 表示未删除

    def to_dict(self, include_deleted=False):
        """
        将模型转换为字典
        :param include_deleted: 是否包含 deleted_time
        :return: 字典对象
        """
        result = {
            c.name: getattr(self, c.name) for c in self.__table__.columns if c.name != 'deleted_time'
        }

        # 格式化时间字段
        result['create_time'] = self.format_datetime(self.create_time)
        result['update_time'] = self.format_datetime(self.update_time)

        # 如果需要返回 deleted_time，可以通过参数控制
        if include_deleted and self.deleted_time:
            result['deleted_time'] = self.format_datetime(self.deleted_time)

        return result
        
    def save(self):
        """
        保存当前实例
        """
        db.session.add(self)
        db.session.commit()

    def update(self, fields: dict):
        """
        更新当前实例的字段
        """
        for key, value in fields.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.update_time = datetime.now(BEIJING_TIMEZONE)
        db.session.commit()

    def soft_delete(self):
        """
        逻辑删除，设置 deleted_time 为当前时间
        """
        self.deleted_time = datetime.now(BEIJING_TIMEZONE)
        db.session.commit()

    def restore(self):
        """
        恢复逻辑删除，将 deleted_time 设置为 None
        """
        self.deleted_time = None
        db.session.commit()

    def delete(self):
        """
        物理删除当前实例
        """
        db.session.delete(self)
        db.session.commit()

    @classmethod
    def get_by_id(cls, obj_id):
        """
        根据 ID 查询未删除的实例
        """
        return cls.query.filter_by(id=obj_id).first()

    @classmethod
    def get_query(cls, exclude_deleted=True):
        query = cls.query
        if exclude_deleted:
            query = query.filter_by(deleted_time=None)
        return query
    
    @classmethod
    def all(cls, include_deleted=False):
        """
        获取所有实例
        """
        query = cls.query
        if not include_deleted:
            query = query.filter_by(deleted_time=None)
        return query.all()

    @staticmethod
    def format_datetime(dt_obj, format='%Y-%m-%d %H:%M:%S'):
        if not dt_obj or type(dt_obj) == str:
            return dt_obj
        return dt_obj.strftime(format)