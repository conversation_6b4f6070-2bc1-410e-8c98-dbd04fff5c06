import json
import logging

from flask import g
from app.models.resource import Resource
from app.service.user import UserService
from app.utils.constant import SystemPerm
from app.utils.redis_utils import ResourceRedisUtils
from app.utils.response import fail_response, success_response

logger = logging.getLogger('flask_app')

class ResourceService:
    
    @staticmethod
    def check_permission(resource_name, action):
        # 先从redis中获取
        logger.debug(f"resource_name: {resource_name}, action: {action}")
        resource = ResourceRedisUtils.get_resource_info(resource_name, action)
        if resource:
            logger.debug(f"resource: {resource}")
            resource = json.loads(resource)
        else:
            resource = Resource.get_resource_by_name_and_action(resource_name, action)
            if not resource:
                logger.error(f"{action}:{resource_name} 不存在!")
                return fail_response(404, f"{action}:{resource_name} 不存在!")
            resource = resource.to_dict()
            ResourceRedisUtils.set_resource_info(resource_name, action, json.dumps(resource))
        # 获取用户权限
        user_info = UserService.get_userinfo(g.user.username)
        if not user_info:
            return fail_response(401, "获取用户信息失败")
        # 判断用户是否有权限
        permission_ids = user_info.get('permission_ids', [])
        resource_perm_id = resource.get('perm_id')
        logger.debug(f"user permission_ids: {permission_ids}")
        logger.debug(f"resource perm_id: {resource_perm_id}")
        if resource.get('perm_id') == SystemPerm.DENY_ALL:
            return fail_response(403, "无权限访问")
        if resource_perm_id in [*permission_ids, SystemPerm.ALLOW_ALL]:
            return success_response("有权限访问")
        return fail_response(403, "无权限访问")

    @staticmethod
    def get_resource_by_name_and_action(name, aciton):
        # 先从redis中获取
        resource = ResourceRedisUtils.get_resource_info(name, aciton)
        if resource:
            resource = json.loads(resource)
        else:
            resource = Resource.get_resource_by_name_and_action(name, aciton)
            if resource:
                ResourceRedisUtils.set_resource_info(name, aciton, json.dumps(resource.to_dict()))