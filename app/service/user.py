import base64
import json
from sqlalchemy import and_, or_, text
from flask_jwt_extended import create_access_token
from app.models.organization import Organization
from app.models.user import User
from flask import request, g
import logging
from app.models.user_role import UserRole
from app.utils.response import fail_response, success_response
from app.utils.common import compare_password
from app.utils import redis_utils
from app.utils.redis_utils import UserRedisUtils
from app.extension import db
from app.models.role import Role
from app.models.tables import organization_code_tmp
logger = logging.getLogger('flask_app')

class UserService:


    @staticmethod
    def login(username:str, password: str):
        user = User.query.filter_by(username=username).first()
        if not user:
            return fail_response(404, "用户不存在")
        if not compare_password(password, user.password):
            return fail_response(400, "认证失败，请检查用户名或者密码是否正确")
        if user.manager not in ['admin', 'super']:
            return fail_response(400, "认证成功，但禁止登录")
        if not user.is_valid():
            return fail_response(403, "用户已被禁用")
        token, roles = UserService.create_token(user)
        userInfo = user.to_dict()
        userInfo['role_ids'] = roles
        # Get the login IP address
        login_ip = request.remote_addr
        userInfo['login_ip'] = login_ip
        logger.debug(login_ip)
        # result = redis_utils.set_key(f"user:{user.username}", json.dumps(userInfo), ex=60 * 60 * 24)
        result = UserRedisUtils.set_userinfo(user.username, json.dumps(userInfo))
        if not result:
            logger.error("redis set key error")
            return fail_response(500, "redis set key error")
        # result = redis_utils.set_key(f"user:{user.username}:token", token, ex=60 * 60 * 24)
        result = UserRedisUtils.set_user_token(user.username, token)
        if not result:
            logger.error("redis set key error")
            return fail_response(500, "redis set key error")
        return success_response({
            'token': token,
            'userInfo': userInfo,
            'rolePermissions': {
                'role_ids': roles,
                'permission_ids' : userInfo.get('permission_ids', [])
            }
        })
    

    @staticmethod
    def logout():
        return UserRedisUtils.delete_user_by_username(g.user.username)

    @staticmethod
    def info():
        user_info = g.user.to_dict()
        user = User.query.filter_by(username=user_info.get('username')).first()
        if not user:
            return fail_response(404, "用户不存在")
        user_info = user.to_dict()
        logger.info("user_info:{user_info}")
        logger.info(user_info)
        UserRedisUtils.set_userinfo(user.username, json.dumps(user_info))
        data = {
            'userInfo': user_info,
            'rolePermissions': {
                'role_ids': user_info.get('role_ids', []),
                'permission_ids': user_info.get('permission_ids', [])
            }
        }
        return success_response(data)

    @staticmethod
    def get_login_url():
        from app.config import ZhuyunOauthConfig
        login_redirect_url = request.args.get('loginRedirectUrl', '')
        origin_url = request.args.get('originUrl', '')
        
        # 构造 state 并进行 base64url 编码
        state = json.dumps({
            'loginRedirectUrl': login_redirect_url,
            'originUrl': origin_url
        })
        state = base64.urlsafe_b64encode(state.encode('utf-8')).decode('utf-8').rstrip("=")
        
        url = (f"{ZhuyunOauthConfig.HOST}/oauth2/authorize"
               f"?redirect_uri={ZhuyunOauthConfig.REDIRECT_URI}&state={state}"
               f"&client_id={ZhuyunOauthConfig.CLIENT_ID}&response_type={ZhuyunOauthConfig.RESPONSE_TYPE}")
        logger.info(f"login url:{url}")
        return url

    @staticmethod
    def getUsersByComCode(**args):
        key = args.get('key')
        com_code = args.get('comCode')
        roles = args.get('roles', [])
        order = args.get('order')

        if not com_code:
            return fail_response(400, "参数错误，comCode不能为空")
        
        # 基本查询条件
        filters = [User.status == "1"]
        if key:
            filters.append(or_(
                User.username.like(f"%{key}%"),
                User.nickname.like(f"%{key}%"),
                User.tel.like(f"%{key}%"),
                User.com_name.like(f"%{key}%"),
                User.dep_name.like(f"%{key}%")
            ))

        if com_code:
            filters.append(User.com_code == com_code)

        # 关联 UserRole 模型
        role_filters = []
        if roles:
            role_filters.append(UserRole.role_id.in_(roles))

        # 排序
        order_by = User.username.desc() if order == "-username" else User.username.asc()

        # 执行查询
        query = User.query.filter(*filters).order_by(order_by)
        if roles:
            query = query.join(UserRole).filter(*role_filters)

        # 指定返回字段
        # users = query.with_entities(
        #     User.username.label("code"),
        #     User.nickname.label("user_name"),
        #     User.com_code,
        #     User.com_name,
        #     User.dep_name,
        #     User.com_level,
        #     User.dep_code,
        #     User.dep_level
        # ).all()
        users = query.all()

        # 数据格式化
        result = [user.to_dict() for user in users]

        return result
    
    @staticmethod
    def create_token(user):
        roles = [role.id for role in user.roles] or []
        logger.debug(roles)
        claims = User.get_jwt_claims(user)
        claims['user_role'] = roles
        token = create_access_token(identity=user.username, additional_claims=claims)
        return token, roles

    @staticmethod
    def get_user_list(**kwargs):
        # 获取请求参数
        limit = kwargs.get('limit', 10)
        page = kwargs.get('page', 1)
        key = kwargs.get('search', '') or ''  # 去除两端空格
        code = kwargs.get('code', '') or ''  # 去除两端空格
        key = key.strip()
        code = code.strip()
        order = kwargs.get('order', 'username:ASC')  # 默认按照 username 升序
        dep_code = kwargs.get('dep_code', '') or ''  # 去除两端空格
        com_level = kwargs.get('com_level', '') or ''  # 获取 com_level 参数
        filter_disabled = kwargs.get('filter_disabled', False)  # 是否过滤禁用用户
        logger.info(f"Pagination params: page={page}, limit={limit}")
        logger.info(f"Filter disabled users: {filter_disabled}")

        # 获取所有子级 code
        codes = []
        if code:
            codes = Organization.get_all_children_codes(code)

        # 如果 codes 不为空，将其存入临时表
        if codes:
            # 清空临时表
            db.session.execute(text("TRUNCATE TABLE USER_CENTER.ORGANIZATION_CODE_TMP"))

            # 将 codes 插入临时表
            for code in codes:
                db.session.execute(
                    text("INSERT INTO USER_CENTER.ORGANIZATION_CODE_TMP (CODE) VALUES (:code)"),
                    {"code": code}
                )
            db.session.commit()

        # 基础查询
        query = User.get_query().distinct(User.id)

        # 过滤禁用用户
        if filter_disabled:
            query = query.filter(
                and_(
                    User.is_disabled != True,
                    User.is_locked != True,
                    User.status != 0
                )
            )

        # 动态添加过滤条件
        if key:
            query = query.filter(
                (User.username.ilike(f"%{key}%")) | 
                (User.nickname.ilike(f"%{key}%"))
            )
        logger.info(f"codes length: {len(codes)}")
        if codes:
            # 使用临时表进行 JOIN 查询
            # query = query.filter(
            #     (User.com_code.in_(codes)) | 
            #     (User.dep_code.in_(codes))
            # )
            # 定义临时表的结构
            query = query.join(
                organization_code_tmp,
                or_(
                    User.com_code == organization_code_tmp.c.CODE,
                    User.dep_code == organization_code_tmp.c.CODE
                )
            )
        if dep_code:
            query = query.filter(or_(User.dep_code == dep_code, User.com_code == dep_code))
        
        # 添加 com_level 过滤条件
        if com_level:
            com_level = com_level.strip()
            if com_level:
                # 支持多个值用逗号分隔
                com_levels = [level.strip() for level in com_level.split(',') if level.strip()]
                if com_levels:
                    query = query.filter(User.com_level.in_(com_levels))
                    logger.info(f"Filtering by com_level: {com_levels}")
        
        # 处理排序
        query = query.order_by(getattr(User, 'status').desc()).order_by(getattr(User, 'username').asc())
        # 使用 paginate 方法进行分页
        paginated_users = query.paginate(page=page, per_page=limit, error_out=False)

        # 提取用户和总记录数
        total = paginated_users.total
        users = paginated_users.items

        logger.info(f"Total records: {total}")
        logger.info(f"Current page items: {len(users)}")

        # 转换为字典格式
        user_dicts = [
            user.to_dict()
            for user in users
        ]

        result = {
            "total": total,
            "page": page,
            "limit": limit,
            "items": user_dicts
        }

        return success_response(result)

   
    @staticmethod
    def get_userinfo(username:str):
        user_info = UserRedisUtils.get_userinfo(username)
        if user_info:
            user_info = json.loads(user_info)
            return user_info
        user = User.query.filter_by(username=username).first()
        if user:
            return user.to_dict()
        return None

    @staticmethod
    def delete_user_by_username(username: str):
        user = User.query.filter_by(username=username).first()
        if not user:
            return fail_response(404, f"用户 {username} 不存在")

        try:
            # 删除用户的关联角色
            UserRole.query.filter_by(username=username).delete()

            # 删除用户
            db.session.delete(user)
            db.session.commit()

            # 从 Redis 中移除用户信息和 token
            UserRedisUtils.delete_user_by_username(username)

            logger.info(f"用户 {username} 已被删除")
            return success_response(f"用户 {username} 已成功删除")
        except Exception as e:
            db.session.rollback()
            logger.error(f"删除用户 {username} 失败: {str(e)}")
            return fail_response(500, f"删除用户失败: {str(e)}")

    @staticmethod
    def update_user_by_username(username: str, data: dict):
        user = User.query.filter_by(username=username).first()
        if not user:
            return fail_response(404, f"用户 {username} 不存在")
        try:
            # 定义允许更新的字段
            allowed_fields = [
                'nickname',
                'email',
                'tel',
                'fullname',
                'sex',
                'birthday',
                'dep_level',
                'dep_name',
                'dep_code',
                'com_level',
                'com_name',
                'com_code',
                'job_name',
                'job_code',
                'office_phone',
                'identity_card',
                'status',
                'manager'
                # 根据需要添加其他可更新字段
            ]
            
            for field in allowed_fields:
                if field in data:
                    setattr(user, field, data[field])
            
            db.session.commit()
            
            # 获取更新后的用户信息
            updated_user = User.query.filter_by(username=username).first()
            user_info = updated_user.to_dict()
            
            # 更新 Redis 中的用户信息
            UserRedisUtils.set_userinfo(username, json.dumps(user_info))
            logger.info(f"用户 {username} 信息已更新")
            return success_response(user_info)
        
        except Exception as e:
            db.session.rollback()
            logger.error(f"更新用户 {username} 信息失败: {str(e)}")
            return fail_response(500, f"更新用户信息失败: {str(e)}")

    @staticmethod
    def set_roles_for_user(username: str, role_ids: list):
        """
        设置用户角色，每次调用都会替换用户现有的角色。
        :param username: 用户名
        :param role_ids: 角色ID列表
        :return: 响应
        """
        user = User.query.filter_by(username=username).first()
        if not user:
            return fail_response(404, f"用户 {username} 不存在")
        
        # 验证角色ID是否存在
        valid_roles = Role.query.filter(Role.id.in_(role_ids)).all()
        valid_role_ids = set(role.id for role in valid_roles)
        invalid_role_ids = set(role_ids) - valid_role_ids
        if invalid_role_ids:
            return fail_response(400, f"无效的角色ID: {', '.join(invalid_role_ids)}")
        
        try:
            # 删除用户现有的所有角色
            UserRole.query.filter_by(username=username).delete()

            # 绑定新的角色
            for role_id in role_ids:
                user_role = UserRole(username=username, role_id=role_id)
                db.session.add(user_role)
            
            db.session.commit()

            # 清除 Redis 中的用户信息和 token
            UserRedisUtils.delete_user_by_username(username)

            logger.info(f"用户 {username} 设置角色 {role_ids} 成功")
            return success_response({
                "message": f"用户 {username} 设置角色成功",
                "user_info": user.to_dict()
            })
        
        except Exception as e:
            db.session.rollback()
            logger.error(f"设置角色给用户 {username} 失败: {str(e)}")
            return fail_response(500, f"设置角色失败: {str(e)}")


    @staticmethod
    def create_user(data: dict):
        """
        创建新用户
        :param data: 用户数据字典
        :return: 响应
        """
        username = data.get('username')
        
        # 验证必填字段
        required_fields = ['username', 'nickname', 'com_code', 'status', 'manager']
        for field in required_fields:
            if field not in data or not data[field]:
                return fail_response(400, f"缺少必填字段: {field}")
        
        # 检查用户名是否已存在
        existing_user = User.query.filter_by(username=username).first()
        if existing_user:
            return fail_response(409, f"用户名 {username} 已存在")
        
        try:
            # 创建新用户
            user = User()
            
            # 允许设置的字段
            allowed_fields = [
                'username',
                'nickname',
                'email',
                'tel',
                'fullname',
                'sex',
                'birthday',
                'dep_level',
                'dep_name',
                'dep_code',
                'com_level',
                'com_name',
                'com_code',
                'job_name',
                'job_code',
                'office_phone',
                'identity_card',
                'status',
                'manager',
                'appID'
                # 根据需要添加其他字段
            ]
            
            data['fullname'] = data.get('fullname', data['nickname'])
            for field in allowed_fields:
                if field in data:
                    setattr(user, field, data[field])
            
            # 保存用户
            db.session.add(user)
            db.session.commit()
            
            # 如果有提供应用ID列表，设置用户的appIDs字段
            # if 'appIDs' in data and isinstance(data['appIDs'], list):
            #     user.appIDs = data['appIDs']
            #     db.session.commit()
            
            logger.info(f"用户 {username} 创建成功")
            return success_response(user.to_dict())  # 201 Created
        except Exception as e:
            db.session.rollback()
            logger.error(f"创建用户失败: {str(e)}")
            return fail_response(500, f"创建用户失败: {str(e)}")
