import requests
from urllib.parse import urlencode
from requests.adapters import HTTPAdapter
from urllib3.util.ssl_ import create_urllib3_context

# 自定义 HTTPS 适配器，支持旧版 SSL/TLS
class LegacyHTTPSAdapter(HTTPAdapter):
    def init_poolmanager(self, *args, **kwargs):
        ctx = create_urllib3_context()
        ctx.options |= 0x4  # 添加 SSL_OP_LEGACY_SERVER_CONNECT
        kwargs['ssl_context'] = ctx
        ctx.check_hostname = False  # 禁用主机名验证
        ctx.verify_mode = 0  # 将证书验证模式设置为 CERT_NONE
        super().init_poolmanager(*args, **kwargs)

class OauthService:
    def __init__(self, code, zy_conf):
        self.code = code
        self.host = zy_conf['host']
        self.client_id = zy_conf['client_id']
        self.client_secret = zy_conf['client_secret']
        self.grant_type = zy_conf['grant_type']
        self.session = requests.Session()
        self.session.mount('https://', LegacyHTTPSAdapter())  # 配置 HTTPS 适配器

    def get_user_info(self):
        """
        获取用户信息
        """
        data = self.get_access_token()
        if not data.get('success'):
            return data
        return self.get_user_info_by_access_token(data.get('access_token'))

    def get_user_uid(self):
        """
        获取用户 UID
        """
        data = self.get_access_token()
        if not data.get('success'):
            return ""
        return data.get('uid', "")

    def get_access_token(self):
        """
        获取 Access Token
        """
        args = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": self.code,
            "grant_type": self.grant_type,
        }
        url = f"{self.host}/oauth2/getToken?{urlencode(args)}"
        print(f"Get access token URL: {url}")

        try:
            response = self.session.post(url, timeout=10, verify=False)
            if 200 <= response.status_code < 300:
                return {"success": True, **response.json()}
            return {"success": False, "error": f"Failed to get access token, status code: {response.status_code}"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_user_info_by_access_token(self, access_token):
        """
        使用 Access Token 获取用户信息
        """
        url = f"{self.host}/oauth2/getUserInfo?client_id={self.client_id}&access_token={access_token}"
        try:
            response = self.session.get(url, timeout=10, verify=False)
            if 200 <= response.status_code < 300:
                return {"success": True, **response.json()}
            return {"success": False, "error": f"Failed to get user info, status code: {response.status_code}"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_wolf_access_token(self, app_id, username, password, wolf_conf):
        """
        获取 Wolf 系统的 Access Token
        """
        url = f"{wolf_conf['host']}/wolf/rbac/login.rest"
        data = {
            "appid": app_id,
            "username": username,
            "password": password,
            "authType": wolf_conf.get('auth_type', 1),
        }

        try:
            response = self.session.post(url, json=data, timeout=10, verify=False)
            if 200 <= response.status_code < 300 and response.json().get('token'):
                return response.json().get('token')
            print(f"Get wolf access token failed, status_code: {response.status_code}")
            return ""
        except Exception as e:
            print(f"Not found token: {str(e)}")
            return ""

