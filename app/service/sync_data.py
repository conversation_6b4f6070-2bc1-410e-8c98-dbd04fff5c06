# services/sync_data_service.py

import json
import time
import requests
from app.config import ZhuyunSyncDataConfig
import logging
from datetime import datetime

from app.models.organization import Organization
from app.models.user import User
from app.models.task_log import TaskLog
from app.models.task_failure import TaskFailure
from app.extension import db
from app.utils.logger import setup_task_logger
setup_task_logger(log_dir='./logs/task', log_level=logging.DEBUG)
logger = logging.getLogger('task_logger')

from requests.packages.urllib3.exceptions import InsecureRequestWarning

# 禁用警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

import ssl
from requests.adapters import HTTPAdapter
from urllib3.util.ssl_ import create_urllib3_context
import requests

class LegacyHTTPSAdapter(HTTPAdapter):
    def init_poolmanager(self, *args, **kwargs):
        ctx = create_urllib3_context()
        ctx.options |= 0x4  # SSL_OP_LEGACY_SERVER_CONNECT
        ctx.set_ciphers('DEFAULT:@SECLEVEL=1')  # 更改加密套件配置
        ctx.minimum_version = ssl.TLSVersion.TLSv1_2  # 设置为 TLS 1.2
        kwargs['ssl_context'] = ctx
        ctx.check_hostname = False  # 禁用主机名验证
        ctx.verify_mode = ssl.CERT_NONE  # 验证模式设置为不检查
        super().init_poolmanager(*args, **kwargs)

class InvalideTokenException(Exception):
    pass
class SyncDataInvalidException(Exception):
    pass

class SyncDataService:
    def __init__(self, token_id=""):
        self.api_url = ZhuyunSyncDataConfig.API_URL
        self.system_code = ZhuyunSyncDataConfig.SYSTEM_CODE
        self.integration_key = ZhuyunSyncDataConfig.INTEGRATION_KEY
        self.token_id = token_id
        self.current_time = int(time.time())
        self.session = requests.Session()
        self.session.mount('https://', LegacyHTTPSAdapter())  # 配置 HTTPS 适配器
        self.task_log = None
        self.total_records = 0
        self.max_retry_count = 3  # 最大重试次数

    def _get_timestamp(self):
        return int(time.time())

    def create_json_request(self, params):
        params['timestamp'] = self._get_timestamp()
        return json.dumps(params)

    def login(self):
        request_arg = self.create_json_request({
            'systemCode': self.system_code,
            'integrationKey': self.integration_key,
            'force': "true",
        })
        url = f"{self.api_url}?method=login&request={request_arg}"
        logger.info(f"url: {url}")
        
        response = self.session.get(url, verify=False, timeout=(10, 10))
        if not response.text:
            logger.warning("Response coentent is empty, finish !!")
            return False
        res = response.json()
        if not res.get('success'):
            logger.error("Login failed", res)
            return False
        
        self.token_id = res.get('tokenId')
        logger.info(f"login success, tokenId: {self.token_id}")
        return True

    def logout(self):
        request_arg = self.create_json_request({'tokenId': self.token_id})
        url = f"{self.api_url}?method=logout&request={request_arg}"

        response = self.session.get(url, verify=False)
        if response.status_code == 200:
            logger.info("Logout successful")
            return True
        else:
            logger.warning(f"Logout failed with status code {response.status_code}")
            return False

    def check_running_task(self, task_name):
        """检查是否有正在运行的任务"""
        running_task = TaskLog.query.filter_by(task_name=task_name, status='running').first()
        if running_task:
            # 如果上次任务开始时间超过12小时，认为是异常中断，更新为失败状态
            time_diff = datetime.now() - running_task.start_time
            if time_diff.total_seconds() > 3600 * 12:  # 12小时 = 43200秒
                logger.warning(f"发现长时间运行的任务 {task_name}，ID: {running_task.id}，将标记为失败")
                running_task.status = 'failed'
                running_task.end_time = datetime.now()
                running_task.message = '任务异常中断，超过12小时未完成'
                db.session.commit()
                return False
            else:
                logger.warning(f"任务 {task_name} 正在运行中，跳过本次执行")
                return True
        return False

    def create_task_log(self, task_name, token_id):
        """创建任务日志"""
        task_log = TaskLog(task_name=task_name, token_id=token_id)
        db.session.add(task_log)
        db.session.commit()
        logger.info(f"创建任务日志: {task_log.id}, 任务名称: {task_name}")
        return task_log

    def update_task_log(self, status, message='', total_records=None):
        """更新任务日志状态"""
        if self.task_log:
            self.task_log.status = status
            self.task_log.end_time = datetime.now()
            self.task_log.message = message
            if total_records is not None:
                self.task_log.total_records = total_records
            else:
                self.task_log.total_records = self.total_records
            db.session.add(self.task_log)
            db.session.commit()
            logger.info(f"更新任务日志: {self.task_log.id}, 状态: {status}, 记录数: {self.task_log.total_records}")

    def safe_get_record_id(self, data):
        """安全获取记录ID"""
        if not data:
            return 'unknown'
        
        # 尝试按照优先级获取ID
        record_id = data.get('code', '')
        if not record_id:
            record_id = data.get('id', '')
        if not record_id:
            record_id = data.get('_user', '')
        if not record_id:
            record_id = data.get('username', 'unknown')
            
        return record_id

    def add_failure_record(self, data, error_msg, data_type):
        """添加失败记录"""
        if not self.task_log:
            logger.error("没有活动的任务日志，无法添加失败记录")
            return None
        
        # 确保data不为None
        if data is None:
            data = {}
            logger.warning("数据为空，使用空字典代替")
        
        # 安全获取记录ID
        record_id = self.safe_get_record_id(data)
        
        try:
            # 检查该记录是否已存在失败记录
            existing_failure = TaskFailure.query.filter_by(
                task_log_id=self.task_log.id,
                record_id=record_id,
                data_type=data_type
            ).first()
            
            if existing_failure:
                # 更新现有记录
                existing_failure.try_count += 1
                existing_failure.failure_detail = str(error_msg)
                existing_failure.update_time = datetime.now()
                db.session.commit()
                logger.info(f"更新失败记录: ID {existing_failure.id}, 记录ID {record_id}, 尝试次数 {existing_failure.try_count}")
                return existing_failure
            else:
                # 创建新记录
                try:
                    # 限制错误信息长度
                    short_reason = str(error_msg)
                    if len(short_reason) > 500:
                        short_reason = short_reason[:497] + "..."
                    
                    # 尝试序列化数据，如果失败则使用安全转换
                    try:
                        row_data_json = json.dumps(data)
                    except Exception as e:
                        logger.warning(f"序列化数据失败: {e}, 使用字符串表示")
                        row_data_json = str(data)
                        
                    failure = TaskFailure(
                        task_log_id=self.task_log.id,
                        failure_reason=short_reason,
                        record_id=record_id,
                        failure_detail=str(error_msg),
                        data_type=data_type,
                        try_count=1,  # 初始尝试次数为1
                        row_data=row_data_json,
                        result='failed'  # 默认状态为失败
                    )
                    db.session.add(failure)
                    db.session.commit()
                    logger.info(f"添加失败记录: 任务ID {self.task_log.id}, 记录ID {record_id}, 类型 {data_type}")
                    return failure
                except Exception as e:
                    logger.error(f"创建失败记录出错: {e}", exc_info=True)
                    # 如果无法创建failure记录，我们仍然继续，不阻止同步过程
                    return None
        except Exception as e:
            logger.error(f"处理失败记录时出错: {e}", exc_info=True)
            return None

    def update_failure_record_result(self, failure_record, success=False, message=None):
        """更新失败记录的结果"""
        if not failure_record:
            return
            
        try:
            failure_record.result = 'success' if success else 'failed'
            if message:
                failure_record.failure_detail = message
            db.session.commit()
            logger.info(f"更新失败记录结果: ID {failure_record.id}, 结果: {failure_record.result}")
        except Exception as e:
            logger.error(f"更新失败记录结果时出错: {e}", exc_info=True)

    def check_max_retries_and_finish(self, failure_record, data, method_type):
        """检查是否达到最大重试次数，如果是则调用finish方法"""
        if not failure_record or failure_record.try_count <= self.max_retry_count:
            return False
            
        logger.warning(f"记录 {failure_record.record_id} 已达到最大重试次数 {self.max_retry_count}，将调用finish方法结束处理")
        
        try:
            # 根据任务类型调用不同的finish方法
            record_id = self.safe_get_record_id(data)
            guid = f"{record_id}_{self.current_time}"
            
            # 获取对象类型，默认为unknown_type
            obj_type = data.get('objectType', 'unknown_type') if isinstance(data, dict) else 'unknown_type'
            
            if method_type == 'pull':
                args = {
                    'taskId': data.get('taskId', data.get('id', 'unknown')) if isinstance(data, dict) else 'unknown',
                    'guid': guid,
                    'success': False,
                    'message': f"达到最大重试次数 {self.max_retry_count}，跳过处理",
                    'objectType': obj_type,
                    'objectCode': record_id,
                    'id': data.get('id', 'unknown') if isinstance(data, dict) else 'unknown'
                }
                self.pull_finish(args)
            else:  # sync
                args = {
                    'objectType': obj_type,
                    'objectCode': record_id,
                    'id': data.get('id', 'unknown') if isinstance(data, dict) else 'unknown',
                    'guid': guid,
                    'success': False
                }
                self.sync_finish(args)
                
            # 更新失败记录的结果
            self.update_failure_record_result(
                failure_record, 
                success=False, 
                message=f"达到最大重试次数 {self.max_retry_count}，任务被标记为结束"
            )
            return True
        except Exception as e:
            logger.error(f"处理最大重试次数时出错: {e}", exc_info=True)
            return False

    def ensure_task_completed(self, app, status='failed', message='任务异常终止'):
        """确保任务被标记为完成状态"""
        if self.task_log and self.task_log.status == 'running':
            with app.app_context():
                self.update_task_log(status=status, message=message)
                logger.warning(f"任务 {self.task_log.task_name} 被强制标记为 {status} 状态")
                return True
        return False

    def pull_task(self, app):
        success = False
        
        # 检查是否有正在运行的任务
        with app.app_context():
            if self.check_running_task('pullTask'):
                return False
            
            # 如果没有token_id，先登录获取
            if not self.token_id:
                if not self.login():
                    logger.warning("Login failed, cannot start pull task")
                    return False
                logger.info(f"登录成功，获取token_id: {self.token_id}")
            
            # 创建任务日志 - 使用最新的token_id
            self.task_log = self.create_task_log('pullTask', self.token_id)
        
        try:
            # 确保token_id有效
            if not self.token_id:
                with app.app_context():
                    self.update_task_log(status='failed', message='未获取有效的token_id')
                return False
            
            request_arg = self.create_json_request({'tokenId': self.token_id})
            url = f"{self.api_url}?method=pullTask&request={request_arg}"
            logger.info(f"url: {url}")
            
            with app.app_context():
                self._fetch_data_and_save(url, self.pull_finish, 'pull')
            
            success = True  # 设置成功标志
            
        except InvalideTokenException:
            logger.warning("Invalid token, retrying login")
            self.token_id = None
            
            try:
                if not self.login():
                    with app.app_context():
                        self.update_task_log(status='failed', message='令牌无效且重新登录失败')
                    return False
                
                # 更新task_log中的token_id
                if self.task_log:
                    with app.app_context():
                        self.task_log.token_id = self.token_id
                        db.session.commit()
                        logger.info(f"更新任务日志token_id: {self.token_id}")
                
                request_arg = self.create_json_request({'tokenId': self.token_id})
                url = f"{self.api_url}?method=pullTask&request={request_arg}"
                logger.info(f"url: {url}")
                
                with app.app_context():
                    self._fetch_data_and_save(url, self.pull_finish, 'pull')
                
                success = True  # 设置成功标志
                
            except Exception as e:
                logger.error(f"重试登录后执行任务失败: {str(e)}", exc_info=True)
                with app.app_context():
                    self.update_task_log(status='failed', message=f'重试失败: {str(e)}')
            
        except requests.exceptions.SSLError as ssl_error:
            logger.error(f"SSL error occurred: {ssl_error}")
            with app.app_context():
                self.update_task_log(status='failed', message=f'SSL错误: {str(ssl_error)}')
        
        except Exception as e:
            logger.error(f"执行增量同步任务时出错: {str(e)}", exc_info=True)
            with app.app_context():
                self.update_task_log(status='failed', message=str(e))
        
        finally:
            # 无论发生什么，确保任务状态被更新
            with app.app_context():
                if success:
                    # 如果成功完成，更新为success状态
                    self.update_task_log(status='success', message='任务成功完成')
                    logger.info(f"任务 {self.task_log.task_name} 成功完成")
                    # 确保变更被提交
                    db.session.commit()
                elif self.task_log and self.task_log.status == 'running':
                    # 如果任务还在running状态，则更新为failed
                    self.update_task_log(status='failed', message='任务异常终止')
                    logger.warning(f"任务 {self.task_log.task_name} 被标记为失败状态")
                    # 确保变更被提交
                    db.session.commit()
            
            # 登出前确保所有数据库变更都已提交
            db.session.commit()
            
            # 尝试登出
            try:
                self.logout()
            except Exception as e:
                logger.error(f"登出时出错: {str(e)}")
            
        return success

    def pull_finish(self, args):
        logger.info(f"taskId: {args.get('taskId', 'unknown')}")
        try:
            request_arg = self.create_json_request({
                'tokenId': self.token_id,
                'taskId': args.get('taskId', ''),
                'guid': args.get('guid', ''),
                'success': args.get('success', False),
                'message': args.get('message', ''),
            })
            url = f"{self.api_url}?method=pullFinish&request={request_arg}"
            logger.info(url)
            
            response = self.session.get(url, verify=False)
            res = response.json()

            logger.info(f"Pull finish response: {res}")
            if not res.get('success'):
                logger.error(f"Pull finish failed: {res}")
                return False
            
            logger.info("Pull finish successful")
            return True
        except Exception as e:
            logger.error(f"Pull finish请求出错: {str(e)}", exc_info=True)
            return False

    def sync_task(self, app):
        success = False
        
        # 检查是否有正在运行的任务
        with app.app_context():
            if self.check_running_task('syncTask'):
                return False
            
            # 如果没有token_id，先登录获取
            if not self.token_id:
                if not self.login():
                    logger.warning("Login failed, cannot start sync task")
                    return False
                logger.info(f"登录成功，获取token_id: {self.token_id}")
            
            # 创建任务日志 - 使用最新的token_id
            self.task_log = self.create_task_log('syncTask', self.token_id)
        
        try:
            # 确保token_id有效
            if not self.token_id:
                with app.app_context():
                    self.update_task_log(status='failed', message='未获取有效的token_id')
                return False
            
            request_arg = self.create_json_request({'tokenId': self.token_id})
            url = f"{self.api_url}?method=syncTask&request={request_arg}"
            logger.info(url)
            
            with app.app_context():
                self._fetch_data_and_save(url, self.sync_finish, 'sync')
            
            success = True  # 设置成功标志
                
        except InvalideTokenException:
            logger.warning("Invalid token, retrying login")
            self.token_id = None
            
            try:
                if not self.login():
                    with app.app_context():
                        self.update_task_log(status='failed', message='令牌无效且重新登录失败')
                    return False
                
                # 更新task_log中的token_id
                if self.task_log:
                    with app.app_context():
                        self.task_log.token_id = self.token_id
                        db.session.commit()
                        logger.info(f"更新任务日志token_id: {self.token_id}")
                
                request_arg = self.create_json_request({'tokenId': self.token_id})
                url = f"{self.api_url}?method=syncTask&request={request_arg}"
                logger.info(f"url: {url}")
                
                with app.app_context():
                    self._fetch_data_and_save(url, self.sync_finish, 'sync')
                
                success = True  # 设置成功标志
                
            except Exception as e:
                logger.error(f"重试登录后执行任务失败: {str(e)}", exc_info=True)
                with app.app_context():
                    self.update_task_log(status='failed', message=f'重试失败: {str(e)}')
                
        except requests.exceptions.SSLError as ssl_error:
            logger.error(f"SSL error occurred: {ssl_error}")
            with app.app_context():
                self.update_task_log(status='failed', message=f'SSL错误: {str(ssl_error)}')
        
        except Exception as e:
            logger.error(f"执行全量同步任务时出错: {str(e)}", exc_info=True)
            with app.app_context():
                self.update_task_log(status='failed', message=str(e))
        
        finally:
            # 无论发生什么，确保任务状态被更新
            with app.app_context():
                if success:
                    # 如果成功完成，更新为success状态
                    self.update_task_log(status='success', message='任务成功完成')
                    logger.info(f"任务 {self.task_log.task_name} 成功完成")
                    # 确保变更被提交
                    db.session.commit()
                elif self.task_log and self.task_log.status == 'running':
                    # 如果任务还在running状态，则更新为failed
                    self.update_task_log(status='failed', message='任务异常终止')
                    logger.warning(f"任务 {self.task_log.task_name} 被标记为失败状态")
                    # 确保变更被提交
                    db.session.commit()
            
            # 登出前确保所有数据库变更都已提交
            db.session.commit()
            
            # 尝试登出
            try:
                self.logout()
            except Exception as e:
                logger.error(f"登出时出错: {str(e)}")
            
        return success

    def sync_finish(self, args):
        try:
            request_arg = self.create_json_request({
                'tokenId': self.token_id,
                'objectType': args.get('objectType', ''),
                'objectCode': args.get('objectCode', ''),
                'id': args.get('id', ''),
                'guid': args.get('guid', ''),
                'success': args.get('success', False),
            })

            url = f"{self.api_url}?method=syncFinish&request={request_arg}"
            logger.info(url)

            response = self.session.get(url, verify=False)
            res = response.json()

            if not res.get('success'):
                logger.warning(f"Sync finish failed: {res}")
                return False
            
            logger.info("Sync finish successful")
            return True
        except Exception as e:
            logger.error(f"Sync finish请求出错: {str(e)}", exc_info=True)
            return False

    def _fetch_data_and_save(self, url, callback, method_type):
        finished = False
        while not finished:
            try:
                response = self.session.get(url, verify=False, headers={'Content-Type': 'application/json;charset=UTF-8'})
                # 记录响应信息
                logger.info(f"Response Status Code: {response.status_code}")
                
                if not response.text:
                    logger.warning("Response content is empty, finish !!")
                    finished = True
                    continue
                    
                try:
                    res = response.json()
                except json.decoder.JSONDecodeError as e:
                    logger.error(f"JSON解析错误: {str(e)}")
                    logger.error(f"响应内容: {response.text}")
                    continue
                    
                # 如果是tokenId无效，抛出异常
                if not res.get('success') and res.get('message', '').startswith('InvalidTokenException:Invalid token'):
                    raise InvalideTokenException('Invalid token')
                    
                if not res.get('success'):
                    logger.warning(f"请求不成功: {res}")
                    finished = True
                    continue

                if isinstance(res, str) and len(res) == 0:
                    logger.info("空响应，结束任务")
                    finished = True
                    continue
                
                if 'objectType' not in res or 'data' not in res:
                    logger.warning(f"响应格式不正确: {res}")
                    finished = True
                    continue
                
                # 安全获取记录标识
                record_id = self.safe_get_record_id(res.get('data'))
                object_type = res.get('objectType', 'unknown')
                logger.info(f"====== {object_type} --- {record_id} --- {res.get('data', {}).get('fullname', '无名称')} ======")
                
                save_res = None
                data_type = "organization" if object_type == "TARGET_ORGANIZATION" else "user"
                failure_record = None
                
                try:
                    if object_type == "TARGET_ORGANIZATION":
                        result, save_res = Organization.save_data(res['data'])
                    else:
                        result, save_res = User.save_data(res['data'])
                    
                    if not result:
                        logger.error(f"保存数据失败: {res}")
                        logger.error(save_res)
                        # 记录失败信息
                        failure_record = self.add_failure_record(res['data'], str(save_res), data_type)
                        
                        # 检查是否达到最大重试次数
                        if self.check_max_retries_and_finish(failure_record, res, method_type):
                            logger.warning(f"记录 {record_id} 已达到最大重试次数，已结束处理")
                        continue
                    
                    # 记录成功处理的数据数量
                    self.total_records += 1
                    
                    guid = f"{save_res['code']}_{self.current_time}"
                    callback({
                        **res, 
                        'guid': guid, 
                        'success': True, 
                        'objectType': object_type,
                        'objectCode': save_res['code'],
                        'id': save_res.get('id', res['data'].get('id', ''))
                    })
                    
                except Exception as e:
                    logger.error(f"处理数据时出错: {str(e)}", exc_info=True)
                    # 记录失败信息
                    failure_record = self.add_failure_record(res.get('data', {}), str(e), data_type)
                    
                    # 检查是否达到最大重试次数
                    if self.check_max_retries_and_finish(failure_record, res, method_type):
                        logger.warning(f"记录 {record_id} 已达到最大重试次数，已结束处理")
                        
                    guid = f"{record_id}_{self.current_time}"
                    callback({
                        **res, 
                        'guid': guid, 
                        'success': False, 
                        'message': str(e),
                        'objectType': object_type,
                        'objectCode': record_id,
                        'id': res.get('data', {}).get('id', '')
                    })
                finally:
                    # 如果失败记录存在，检查是否达到最大重试次数
                    if failure_record:
                        self.check_max_retries_and_finish(failure_record, res, method_type)
            except Exception as e:
                logger.error(f"处理数据流时出错: {str(e)}", exc_info=True)
                # 如果发生严重错误，结束处理
                finished = True

