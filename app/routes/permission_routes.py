import logging
from flask_restx import Namespace, Resource, reqparse
from sqlalchemy import or_, and_, desc, asc
from app.models.permission import Permission as PermissionModel
from app.models.role_permission import RolePermission
from app.models.user_role import UserRole as UserRoleModel
from app.models.role import Role as RoleModel
from app.models.resource import Resource as ResourceModel
from app.utils.response import success_response, fail_response
from app.utils.errors import ArgsError
from sqlalchemy.exc import SQLAlchemyError
from app.extension import db  # 使用数据库会话
logger = logging.getLogger("flask_app")
ns = Namespace("permissions", description="Permission Management")

# 请求解析器：用于列表查询
list_parser = reqparse.RequestParser()
list_parser.add_argument('limit', type=int, default=10, help='Number of items per page')
list_parser.add_argument('page', type=int, default=1, help='Page number')
list_parser.add_argument('ids', type=str, action='split', help='List of IDs')
list_parser.add_argument('key', type=str, help='Search key')
list_parser.add_argument('category_id', type=str, help='Category ID')
list_parser.add_argument('order', type=str, default='-id', help='Ordering')

# 请求解析器：用于创建和更新权限
create_update_parser = reqparse.RequestParser()
create_update_parser.add_argument('id', type=str, required=True, help='Permission ID')
create_update_parser.add_argument('name', type=str, required=True, help='Permission name')
create_update_parser.add_argument('description', type=str, help='Description')
create_update_parser.add_argument('category_id', type=str, help='Category ID')

# 请求解析器：用于更新资源
resource_update_parser = reqparse.RequestParser()
resource_update_parser.add_argument('id', type=str, required=True, help='Permission ID')
resource_update_parser.add_argument('resourceIds', type=list, location='json', help='List of resource IDs')


@ns.route('/')
class PermissionListResource(Resource):
    """Resource for permission list and creation"""

    @ns.expect(list_parser)
    def get(self):
        """
        GET: Fetch permissions list
        """
        args = list_parser.parse_args()
        limit = args["limit"]
        page = args["page"]
        offset = (page - 1) * limit
        key = args.get("key")
        ids = args.get("ids")
        category_id = args.get("category_id")
        order = args.get("order")

        query = PermissionModel.get_query()

        if key:
            query = query.filter(or_(
                PermissionModel.id.like(f"%{key}%"),
                PermissionModel.name.like(f"%{key}%")
            ))

        if ids:
            query = query.filter(PermissionModel.id.in_(ids))

        if category_id:
            query = query.filter(PermissionModel.category_id == category_id)
        # 排序    
        query = query.order_by(asc(PermissionModel.id))
        total = query.count()
        permissions = query.offset(offset).limit(limit).all()

        data = {
            "permissions": [permission.to_dict() for permission in permissions],
            "total": total
        }
        return success_response(data)

    @ns.expect(create_update_parser)
    def post(self):
        """
        POST: Create a new permission
        """
        args = create_update_parser.parse_args()
        perm_id = args["id"]
        name = args["name"]
        description = args.get("description")
        category_id = args.get("category_id")

        # Check for duplicates
        if PermissionModel.get_by_id(perm_id):
            return fail_response(400, "Permission ID already exists")
        if PermissionModel.get_query().filter_by(name=name).first():
            return fail_response(400, "Permission name already exists")

        # Create permission
        new_permission = PermissionModel(
            id=perm_id,
            name=name,
            description=description,
            category_id=category_id
        )
        new_permission.save()
        return success_response({"permission": new_permission.to_dict()})


@ns.route('/<string:id>/')
class PermissionDetailResource(Resource):
    """Resource for single permission operations"""

    def get(self, id):
        """
        GET: Fetch a single permission by ID
        """
        permission = PermissionModel.get_by_id(id)
        if not permission:
            return fail_response(404, "Permission not found")
        return success_response({"permission": permission.to_dict()})

    @ns.expect(create_update_parser)
    def put(self, id):
        """
        PUT: Update a permission by ID
        """
        args = create_update_parser.parse_args()
        name = args["name"]
        description = args.get("description")
        category_id = args.get("category_id")

        permission = PermissionModel.get_by_id(id)
        if not permission:
            return fail_response(404, "Permission not found")

        if name:
            duplicate = PermissionModel.get_query().filter(
                PermissionModel.id != id,
                PermissionModel.name == name
            ).first()
            if duplicate:
                return fail_response(400, "Permission name already exists")
            permission.name = name

        permission.description = description
        permission.category_id = category_id
        permission.save()

        return success_response({"permission": permission.to_dict()})

    def delete(self, id):
        """
        DELETE: Delete a permission by ID
        """
        permission = PermissionModel.get_by_id(id)
        if not permission:
            return fail_response(404, "Permission not found")

        # Check if the permission is in use
        in_use = RolePermission.query.filter_by(perm_id=id).first() or ResourceModel.get_query().filter_by(
          perm_id=id
        ).first()
        if in_use:
            logger.error(f"Deleting the permission({id}) failed, it has been used.")
            raise ArgsError("权限已关联角色或资源，无法删除")

        permission.soft_delete()
        return success_response()


@ns.route('/updatePermissionResource/')
class PermissionResourceUpdate(Resource):
    """Resource for updating resources associated with a permission"""

    @ns.expect(resource_update_parser)
    def post(self):
        """
        POST: Update resources for a permission
        """
        args = resource_update_parser.parse_args()
        perm_id = args["id"]
        resource_ids = args.get("resourceIds", [])

        permission = PermissionModel.get_by_id(perm_id)
        if not permission:
            return fail_response(404, "Permission not found")

        # 开始事务
        try:
            # 清空当前关联的资源
            ResourceModel.query.filter_by(perm_id=perm_id).update({"perm_id": None}, synchronize_session='fetch')

            # 更新新的资源关联
            ResourceModel.query.filter(ResourceModel.id.in_(resource_ids)).update({"perm_id": perm_id}, synchronize_session='fetch')

            # 提交事务
            db.session.commit()
        except SQLAlchemyError as e:
            # 如果发生错误，回滚事务
            db.session.rollback()
            logger.error(f"Failed to update resources for permission {perm_id}: {str(e)}")
            return fail_response(500, "Failed to update resources")

        return success_response()