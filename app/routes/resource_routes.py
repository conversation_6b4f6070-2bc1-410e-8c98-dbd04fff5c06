import json
import logging
from flask_restx import Namespace, Resource, reqparse
from sqlalchemy import or_
from app.models.resource import Resource as ResourceModel
from app.models.permission import Permission as PermissionModel
from app.utils.redis_utils import ResourceRedisUtils
from app.utils.response import success_response, fail_response

logger = logging.getLogger("flask_app")
ns = Namespace("resources", description="Resource Management")

# 请求解析器：用于列表查询
list_parser = reqparse.RequestParser()
list_parser.add_argument('limit', type=int, default=10, help='Number of items per page')
list_parser.add_argument('page', type=int, default=1, help='Page number')
list_parser.add_argument('key', type=str, help='Search key (name or match_type)')
list_parser.add_argument('perm_id', type=str, help='Permission ID')
list_parser.add_argument('order', type=str, default='-id', help='Ordering')

# 请求解析器：用于创建和更新资源
create_update_parser = reqparse.RequestParser()
create_update_parser.add_argument('name', type=str, required=True, help='Resource name')
create_update_parser.add_argument('match_type', type=str, required=True, help='Match type')
create_update_parser.add_argument('priority', type=int, default=0, help='Priority')
create_update_parser.add_argument('action', type=str, help='Action')
create_update_parser.add_argument('perm_id', type=str, help='Permission ID')


@ns.route('/')
class ResourceListResource(Resource):
    """Resource for resource list and creation"""

    @ns.expect(list_parser)
    def get(self):
        """
        GET: Fetch resources list
        """
        args = list_parser.parse_args()
        limit = args["limit"]
        page = args["page"]
        offset = (page - 1) * limit
        key = args.get("key")
        perm_id = args.get("perm_id")
        order = args.get("order")

        query = ResourceModel.get_query()
        if key:
            query = query.filter(or_(
                ResourceModel.name.like(f"%{key}%"),
                ResourceModel.match_type.like(f"%{key}%")
            ))

        if perm_id:
            query = query.filter(ResourceModel.perm_id == perm_id)
        query = query.order_by(ResourceModel.create_time.desc())
        total = query.count()
        resources = query.offset(offset).limit(limit).all()

        data = {
            "resources": [resource.to_dict() for resource in resources],
            "total": total
        }
        return success_response(data)

    @ns.expect(create_update_parser)
    def post(self):
        """
        POST: Create a new resource
        """
        args = create_update_parser.parse_args()
        name = args["name"]
        match_type = args["match_type"]
        priority = args.get("priority")
        action = args.get("action")
        perm_id = args.get("perm_id")

        # Check for duplicates
        if ResourceModel.get_by_name_and_match_type(name, match_type):
            return fail_response(400, "Resource with the same name and match type already exists")

        # Validate permission ID
        if perm_id and perm_id not in ('ALLOW_ALL', 'DENY_ALL') and not PermissionModel.get_by_id(perm_id):
            return fail_response(400, "Invalid permission ID")

        # Create resource
        new_resource = ResourceModel(
            name=name,
            match_type=match_type,
            priority=priority,
            action=action,
            perm_id=perm_id
        )
        new_resource.save()
        ResourceRedisUtils.flush_all()
        return success_response({"resource": new_resource.to_dict()})


@ns.route('/<string:id>/')
class ResourceDetailResource(Resource):
    """Resource for single resource operations"""

    def get(self, id):
        """
        GET: Fetch a single resource by ID
        """
        resource = ResourceModel.get_by_id(id)
        if not resource:
            return fail_response(404, "Resource not found")
        return success_response({"resource": resource.to_dict()})

    @ns.expect(create_update_parser)
    def put(self, id):
        """
        PUT: Update a resource by ID
        """
        args = create_update_parser.parse_args()
        name = args.get("name")
        match_type = args.get("match_type")
        priority = args.get("priority")
        action = args.get("action")
        perm_id = args.get("perm_id")

        resource = ResourceModel.get_by_id(id)
        if not resource:
            return fail_response(404, "Resource not found")

        if name and match_type:
            duplicate = ResourceModel.get_query().filter(
                ResourceModel.id != id,
                ResourceModel.name == name,
                ResourceModel.match_type == match_type
            ).first()
            if duplicate:
                return fail_response(400, "Resource with the same name and match type already exists")

        if perm_id and not perm_id in ('ALLOW_ALL', 'DENY_ALL') and not PermissionModel.get_by_id(perm_id):
            return fail_response(400, "Invalid permission ID")

        # Update resource
        resource.name = name or resource.name
        resource.match_type = match_type or resource.match_type
        resource.priority = priority or resource.priority
        resource.action = action or resource.action
        resource.perm_id = perm_id or resource.perm_id
        resource.save()
        resource_info = resource.to_dict()
        # ResourceRedisUtils.set_resource_info(resource.name, resource.action, json.dumps(resource_info))
        ResourceRedisUtils.flush_all()
        return success_response({"resource": resource_info})

    def delete(self, id):
        """
        DELETE: Delete a resource by ID
        """
        resource = ResourceModel.get_by_id(id)
        if not resource:
            return fail_response(404, "Resource not found")

        resource.delete()
        # ResourceRedisUtils.delete_resource_by_name_and_action(resource.name, resource.action)
        ResourceRedisUtils.flush_all()
        return success_response()
