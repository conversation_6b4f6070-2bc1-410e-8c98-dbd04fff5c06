import logging
from flask_restx import Namespace, Resource, reqparse
from sqlalchemy import or_
from app.models.role import Role
from app.models.role_permission import RolePermission
from app.models.permission import Permission
from app.utils.response import success_response, fail_response
from app.utils.errors import ArgsError
from app.extension import db
logger = logging.getLogger('flask_app')
ns = Namespace('roles', description='Role Management')

# 请求解析器：用于列表查询
list_parser = reqparse.RequestParser()
list_parser.add_argument('limit', type=int, default=10, help='Number of roles per page')
list_parser.add_argument('page', type=int, default=1, help='Page number')
list_parser.add_argument('key', type=str, help='Search key (role name or ID)')
list_parser.add_argument('order', type=str, default='-id', help='Ordering')

# 请求解析器：用于创建和更新角色
create_update_parser = reqparse.RequestParser()
create_update_parser.add_argument('name', type=str, required=True, help='Role name')
create_update_parser.add_argument('description', type=str, help='Role description')
create_update_parser.add_argument('id', type=str, help='Role ID')


@ns.route('/')
class RoleListResource(Resource):
    """Resource for role list and creation"""

    @ns.expect(list_parser)
    def get(self):
        """
        GET: Fetch roles list with users and permissions using left outer join
        """
        args = list_parser.parse_args()
        limit = args["limit"]
        page = args["page"]
        key = args.get("key")
        # 查询角色列表
        query = db.session.query(Role).distinct()

        if key:
            query = query.filter(or_(
                Role.id.like(f"%{key}%"),
                Role.name.like(f"%{key}%"),
                # Permission.name.like(f"%{key}%"),
                # Permission.id.like(f"%{key}%")
            ))
        paginated_role = query.order_by(getattr(Role, 'id').asc()).paginate(page=page, per_page=limit, error_out=False)
        total = paginated_role.total
        roles = paginated_role.items

        data = {
            "roles": [
                {
                    **role.to_dict(),
                    # "users": [user.to_dict() for user in role.users],  # 获取用户列表
                    "permissions": [perm.to_dict() for perm in role.permissions]  # 获取权限列表
                }
                for role in roles
            ],
            "total": total
        }
        return success_response(data)

    @ns.expect(create_update_parser)
    def post(self):
        """
        POST: Create a new role
        """
        args = create_update_parser.parse_args()
        id = args["id"]
        name = args["name"]
        description = args.get("description")
        # perm_ids = args.get("perm_ids", [])

        # 检查角色名称是否重复
        if Role.query.filter_by(id=id).first():
            return fail_response(400, "Role name already exists")

        # 创建角色
        new_role = Role(id=id, name=name, description=description)
        new_role.save()

        # 创建角色与权限的关联
        # for perm_id in perm_ids:
        #     permission = Permission.get_by_id(perm_id)
        #     if not permission:
        #         logger.warning(f"Permission ID {perm_id} not found, skipping")
        #         continue
        #     role_permission = RolePermission(role_id=new_role.id, perm_id=perm_id)
        #     role_permission.save()

        return success_response({"role": new_role.to_dict()})


@ns.route('/<string:id>/')
class RoleDetailResource(Resource):
    """Resource for single role operations"""

    def get(self, id):
        """
        GET: Fetch a single role by ID
        """
        role = Role.get_by_id(id)
        if not role:
            return fail_response(404, "Role not found")

        data = {
            **role.to_dict(),
            "users": [user.to_dict() for user in role.users],
            "permissions": [perm.to_dict() for perm in role.permissions]
        }
        return success_response(data)

    @ns.expect(create_update_parser)
    def put(self, id):
        """
        PUT: Update a role by ID
        """
        args = create_update_parser.parse_args()
        name = args.get("name")
        description = args.get("description")
        perm_ids = args.get("perm_ids", [])

        role = Role.get_by_id(id)
        if not role:
            return fail_response(404, "Role not found")

        # 更新角色基本信息
        if name:
            if Role.query.filter(Role.id != id, Role.name == name).first():
                return fail_response(400, "Role name already exists")
            role.name = name
        role.description = description
        role.save()

        # 更新角色与权限的关联
        RolePermission.query.filter_by(role_id=id).delete()  # 清空旧的关联
        for perm_id in perm_ids:
            permission = Permission.get_by_id(perm_id)
            if not permission:
                logger.warning(f"Permission ID {perm_id} not found, skipping")
                continue
            role_permission = RolePermission(role_id=id, perm_id=perm_id)
            role_permission.save()

        return success_response({"role": role.to_dict()})

    def delete(self, id):
        """
        DELETE: Delete a role by ID
        """
        role = Role.get_by_id(id)
        if not role:
            return fail_response(404, "Role not found")

        # 检查角色是否正在被使用
        if role.users:
            logger.error(f"Role {id} is associated with users, cannot delete")
            raise ArgsError("ERR_ROLE_REMOVE_DENIED")

        # 删除角色及其关联的权限
        RolePermission.query.filter_by(role_id=id).delete()
        role.delete()
        return success_response()


@ns.route('/<string:id>/permissions/')
class RolePermissionResource(Resource):
    """Resource for managing permissions of a role"""

    parser = reqparse.RequestParser()
    parser.add_argument('perm_ids', type=list, location='json', required=True, help='List of permission IDs')

    @ns.expect(parser)
    def post(self, id):
        """
        POST: Update permissions for a role
        """
        args = self.parser.parse_args()
        perm_ids = args['perm_ids']

        # 获取角色
        role = Role.get_by_id(id)
        if not role:
            return fail_response(404, "Role not found")

        try:
            # 删除当前所有权限绑定
            RolePermission.query.filter_by(role_id=id).delete()

            # 添加新的权限绑定
            for perm_id in perm_ids:
                permission = Permission.get_by_id(perm_id)
                if not permission:
                    logger.warning(f"Permission ID {perm_id} not found, skipping")
                    continue
                role_permission = RolePermission(role_id=id, perm_id=perm_id)
                role_permission.save()
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            logger.error(f"Failed to update permissions for role {id}: {str(e)}")
            return fail_response(500, "Failed to update role permissions")

        return success_response({"message": "Role permissions updated successfully"})
