import logging
from flask_restx import Namespace, Resource, reqparse
from app.models.organization import Organization
from app.models.role_assign import RoleAssign
from app.models.user import User
from app.models.user_role import UserRole
from app.utils.response import success_response, fail_response
from app.extension import db
from sqlalchemy import or_

logger = logging.getLogger('flask_app')
ns = Namespace('organizations', description='Organization related operations')


@ns.route('/getOrganizations')
class GetOrganizations(Resource):
    """查询所有二级企业"""
    parser = reqparse.RequestParser()
    parser.add_argument('com_level', type=str, default='02', location='args', help='企业层级')

    @ns.expect(parser)
    def get(self):
        """查询所有二级企业"""
        args = self.parser.parse_args()
        com_level = args['com_level']

        try:
            organizations = Organization.get_organizations_by_level(
                com_level,
                org_level_null=True,
                include_virtual=False
            )
            
            # 过滤不包含 'G' 的组织
            filtered_orgs = [
                org for org in organizations 
                if org.fullname and 'G' not in org.fullname
            ]
            
            result = [
                {
                    'name': org.fullname,
                    'code': org.code,
                    'parent_org_code': org.parent_org_code,
                    '_parent': org.parent_org_code,
                    'com_level': org.com_level
                }
                for org in filtered_orgs
            ]
            
            return success_response(result)
        except Exception as e:
            logger.error(f"获取组织列表失败: {e}", exc_info=True)
            return fail_response(500, str(e))


@ns.route('/getTreeOrganizations')
class GetTreeOrganizations(Resource):
    """获取企业的树形结构"""

    def get(self):
        """获取企业的树形结构"""
        try:
            data = Organization.get_all_data(return_tree=True)
            return success_response(data)
        except Exception as e:
            logger.error(f"获取树形组织结构失败: {e}", exc_info=True)
            return fail_response(500, str(e))


@ns.route('/getDepartmentsWithUser')
class GetDepartmentsWithUser(Resource):
    """查询企业下的部门和人员列表"""
    parser = reqparse.RequestParser()
    parser.add_argument('com_code', type=str, required=True, location='args', help='企业代码不能为空')
    parser.add_argument('sub_com_level', type=int, default=1, location='args', help='子级层级')

    @ns.expect(parser)
    def get(self):
        """查询企业下的部门和人员列表"""
        args = self.parser.parse_args()
        com_code = args['com_code']

        try:
            # 获取指定企业下的部门和用户
            tree_data = Organization.get_tree(
                com_code=com_code,
                include_departments=True,
                include_users=True,
                include_virtual_org=False
            )
            
            return success_response(tree_data)
        except Exception as e:
            logger.error(f"获取部门和用户失败: {e}", exc_info=True)
            return fail_response(500, str(e))


@ns.route('/getTreeData')
class GetTreeData(Resource):
    """获取企业的树形结构数据"""
    parser = reqparse.RequestParser()
    parser.add_argument('comCode', type=str, required=False, location='json', help='企业代码')
    parser.add_argument('includeDepartments', type=bool, default=False, location='json', help='是否包含部门')
    parser.add_argument('includeEnterprises', type=bool, default=False, location='json', help='是否包含子企业')
    parser.add_argument('includeUsers', type=bool, default=False, location='json', help='是否包含用户')
    parser.add_argument('includeVirtualOrg', type=bool, default=True, location='json', help='是否包含虚拟组织')
    parser.add_argument('comLevel', type=str, default='', location='json', help='公司级别')
    parser.add_argument('key', type=str, location='json', help='搜索参数')
    parser.add_argument('fromDB', type=bool, location='json', help='是否从数据库查询')
    
    @ns.expect(parser)
    def post(self):
        """获取企业的树形结构数据"""
        def parse_boolean(value):
            """解析布尔值参数"""
            if isinstance(value, str):
                return value.lower() == 'true'
            return bool(value)
            
        args = self.parser.parse_args()
        
        try:
            # 使用优化的方法获取树形数据
            tree_data = Organization.get_all_tree_data_optimized(
                search=args.get('key'),
                include_departments=parse_boolean(args.get('includeDepartments')),
                include_enterprises=parse_boolean(args.get('includeEnterprises')),
                include_users=parse_boolean(args.get('includeUsers')),
                include_virtual_org=parse_boolean(args.get('includeVirtualOrg')),
                com_level=args.get('comLevel'),
                parent_code=args.get('comCode')
            )
            return success_response(tree_data)
        except Exception as e:
            logger.error(f"获取树形数据失败: {e}", exc_info=True)
            return fail_response(500, str(e))


@ns.route('/getEnterpriseDetails')
class GetEnterpriseDetails(Resource):
    """获取二级企业下的三级企业、部门和部门下的人员"""
    parser = reqparse.RequestParser()
    parser.add_argument('comCode', type=str, required=True, location='args', help='企业代码')
    parser.add_argument('includeEnterprise', type=str, default='true', location='args', help='是否包含下级企业')
    
    @ns.expect(parser)
    def get(self):
        """获取二级企业下的三级企业、部门和部门下的人员"""
        args = self.parser.parse_args()
        com_code = args['comCode']
        include_enterprise = True if args.get('includeEnterprise') == 'true' else False
        
        try:
            # 调用模型方法获取企业详情
            result = Organization.get_enterprise_details(com_code, include_enterprise)
            return success_response(result)
            
        except Exception as e:
            logger.error(f"获取企业详情失败: {e}", exc_info=True)
            return fail_response(500, str(e))


@ns.route('/getOrganizationsWithoutRoles')
class GetOrganizationsWithoutRoles(Resource):
    """获取没有分配过角色的组织列表"""
    parser = reqparse.RequestParser()
    parser.add_argument('page', type=int, default=1, location='args', help='页码')
    parser.add_argument('limit', type=int, default=10, location='args', help='每页数量')
    parser.add_argument('key', type=str, location='args', help='搜索关键字')
    parser.add_argument('com_level', type=str, default='02', location='args', help='企业级别')

    @ns.expect(parser)
    def get(self):
        """获取没有分配过角色的组织列表"""
        args = self.parser.parse_args()
        page = args['page']
        limit = args['limit']
        key = args.get('key', '')
        com_level = args.get('com_level')
        
        try:
            query = Organization.query.filter(
                ~Organization.code.in_(db.session.query(RoleAssign.com_code)),
                Organization.com_level == com_level,
                Organization.org_level.is_(None),
                Organization.name.isnot(None),
                Organization.fullname.isnot(None),
                Organization.org_status == '1',
                Organization.is_virtual_org == False,
                Organization.is_disabled == False,
            )

            if key:
                query = query.filter(or_(
                    Organization.name.like(f'%{key}%'),
                    Organization.fullname.like(f'%{key}%')
                ))

            pagination = query.paginate(page=page, per_page=limit, error_out=False)
            result = {
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': pagination.page,
                'per_page': pagination.per_page,
                'items': [item.to_dict() for item in pagination.items]
            }
            return success_response(result)
        except Exception as e:
            logger.error(f"获取无角色组织失败: {e}", exc_info=True)
            return fail_response(500, str(e))


@ns.route('/addRoleAssigns')
class AddRoleAssigns(Resource):
    """新增角色分配记录"""
    parser = reqparse.RequestParser()
    parser.add_argument('role_assigns', type=list, location='json', required=True, help='角色分配记录列表', action='append')

    @ns.expect(parser)
    def post(self):
        """新增角色分配记录"""
        args = self.parser.parse_args()
        role_assigns = args['role_assigns']

        try:
            for role_assign in role_assigns:
                module = role_assign.get('module')
                com_name = role_assign.get('com_name')
                com_code = role_assign.get('com_code')
                user_code = role_assign.get('user_code', '')
                user_name = role_assign.get('user_name', '')
                is_leader = role_assign.get('is_leader', False)
                is_principal = role_assign.get('is_principal', False)
                is_supervisor = role_assign.get('is_supervisor', False)
                is_admin = role_assign.get('is_admin', False)

                new_role_assign = RoleAssign(
                    module=module,
                    com_name=com_name,
                    com_code=com_code,
                    user_code=user_code,
                    user_name=user_name,
                    is_leader=is_leader,
                    is_principal=is_principal,
                    is_supervisor=is_supervisor,
                    is_admin=is_admin
                )
                db.session.add(new_role_assign)

            db.session.commit()
            return success_response("Role assigns added successfully")
        except Exception as e:
            db.session.rollback()
            return fail_response(500, str(e))


@ns.route('/getOrganizationsWithRoles')
class GetOrganizationsWithRoles(Resource):
    """获取已经分配角色的企业列表"""
    parser = reqparse.RequestParser()
    parser.add_argument('module', type=str, required=True, location='args', help='模块名称')
    parser.add_argument('key', type=str, location='args', help='搜索关键字')

    @ns.expect(parser)
    def get(self):
        """获取已经分配角色的企业列表"""
        args = self.parser.parse_args()
        module = args['module']
        key = args.get('key', '')

        try:
            query = db.session.query(
                RoleAssign.com_code,
                RoleAssign.com_name,
                RoleAssign.module,
            ).filter(
                RoleAssign.module == module
            )

            if key:
                query = query.filter(
                    or_(
                        RoleAssign.com_name.ilike(f'%{key}%'),
                        RoleAssign.com_code.ilike(f'%{key}%')
                    )
                )

            data = query.distinct().all()
            result = [{'com_code': row.com_code, 'com_name': row.com_name, 'module': row.module} for row in data]
            return success_response(result)
        except Exception as e:
            logger.error(e, exc_info=True)
            return fail_response(500, str(e))      

@ns.route('/saveRoleAssigns')
class SaveRoleAssigns(Resource):
    """保存角色分配信息"""
    parser = reqparse.RequestParser()
    parser.add_argument('role_assigns', type=list, location='json', required=True, help='角色分配记录列表')

    @ns.expect(parser)
    def post(self):
        """保存角色分配信息"""
        args = self.parser.parse_args()
        role_assigns = args['role_assigns']
        module_role_map = {
            'QUALITY_ASSESSMENT': {
                'is_leader': 'quality_assessment.enterprise.leader',
                'is_principal': 'quality_assessment.enterprise.admin',
                'is_supervisor': 'quality_assessment.other',
                'is_admin': 'quality_assessment.enterprise.admin'
            }
        }
        if len(role_assigns) == 0:
            return fail_response(400, "role_assigns is required")
        try:
            for role_assign in role_assigns:
                module = role_assign.get('module')
                com_name = role_assign.get('com_name')
                com_code = role_assign.get('com_code')
                user_code = role_assign.get('user_code', '')
                user_name = role_assign.get('user_name', '')
                is_leader = role_assign.get('is_leader', False)
                is_principal = role_assign.get('is_principal', False)
                is_supervisor = role_assign.get('is_supervisor', False)
                is_admin = role_assign.get('is_admin', False)

                # 检查 com_code 是否在 Organization 表中存在
                organization = Organization.query.filter_by(code=com_code).first()
                if not organization:
                    return fail_response(400, f"Organization with code {com_code} does not exist")

                # 检查是否已经存在相同的记录
                existing_role_assign = RoleAssign.query.filter_by(
                    module=module,
                    com_code=com_code,
                    user_code=user_code,
                ).first()
                if existing_role_assign:
                    # 更新现有记录
                    existing_role_assign.is_leader = is_leader
                    existing_role_assign.is_principal = is_principal
                    existing_role_assign.is_supervisor = is_supervisor
                    existing_role_assign.is_admin = is_admin
                else:
                    # 创建新记录
                    new_role_assign = RoleAssign(
                        module=module,
                        com_name=com_name,
                        com_code=com_code,
                        user_code=user_code,
                        user_name=user_name,
                        is_leader=is_leader,
                        is_principal=is_principal,
                        is_supervisor=is_supervisor,
                        is_admin=is_admin
                    )
                    db.session.add(new_role_assign)

                # 更新 UserRole 表
                if user_code:
                    roles_to_add = []
                    roles_to_remove = []
                    role_map = module_role_map.get(module, {})
                    if is_leader:
                        roles_to_add.append(role_map.get('is_leader'))
                    else:
                        roles_to_remove.append(role_map.get('is_leader'))
                    if is_principal:
                        roles_to_add.append(role_map.get('is_principal'))
                    else:
                        roles_to_remove.append(role_map.get('is_principal'))
                    if is_supervisor:
                        roles_to_add.append(role_map.get('is_supervisor'))
                    else:
                        roles_to_remove.append(role_map.get('is_supervisor'))
                    if is_admin:
                        roles_to_add.append(role_map.get('is_admin'))
                    else:
                        roles_to_remove.append(role_map.get('is_admin'))

                    # 添加角色
                    for role_id in roles_to_add:
                        if role_id:
                            existing_user_role = UserRole.query.filter_by(username=user_code, role_id=role_id).first()
                            if not existing_user_role:
                                new_user_role = UserRole(username=user_code, role_id=role_id)
                                db.session.add(new_user_role)

                    # 移除角色
                    for role_id in roles_to_remove:
                        if role_id:
                            existing_user_role = UserRole.query.filter_by(username=user_code, role_id=role_id).first()
                            if existing_user_role:
                                db.session.delete(existing_user_role)

            db.session.commit()
            return success_response("Role assigns saved successfully")
        except Exception as e:
            db.session.rollback()
            return fail_response(500, str(e))

@ns.route('/getOrganizationsWithRoles')
class GetOrganizationsWithRoles(Resource):
    """获取已经分配角色的企业列表"""
    parser = reqparse.RequestParser()
    parser.add_argument('module', type=str, required=True, location='args', help='模块名称')
    parser.add_argument('key', type=str, location='args', help='搜索关键字')

    @ns.expect(parser)
    def get(self):
        """获取已经分配角色的企业列表"""
        args = self.parser.parse_args()
        module = args['module']
        key = args.get('key', '')

        try:
            query = db.session.query(
                RoleAssign.com_code,
                RoleAssign.com_name
            ).filter(
                RoleAssign.module == module
            )

            if key:
                query = query.filter(
                    or_(
                        RoleAssign.com_name.ilike(f'%{key}%'),
                        RoleAssign.com_code.ilike(f'%{key}%')
                    )
                )

            data = query.distinct().all()
            result = [{'com_code': row.com_code, 'com_name': row.com_name, 'module': row.module} for row in data]
            return success_response(result)
        except Exception as e:
            return fail_response(500, str(e))
        
@ns.route('/getRoleAssigns')
class GetRoleAssigns(Resource):
    """通过 module 和 com_code 查询已分配角色的企业列表"""
    parser = reqparse.RequestParser()
    parser.add_argument('module', type=str, required=True, location='args', help='模块名称')
    parser.add_argument('com_code', type=str, location='args', help='企业代码')
    parser.add_argument('page', type=int, default=1, location='args', help='页码')
    parser.add_argument('limit', type=int, default=10, location='args', help='每页数量')
    parser.add_argument('key', type=str, location='args', help='搜索关键字')

    @ns.expect(parser)
    def get(self):
        """通过 module 和 com_code 查询已分配角色的企业列表"""
        args = self.parser.parse_args()
        module = args['module']
        com_code = args['com_code']
        page = args['page']
        limit = args['limit']
        key = args.get('key', '')

        try:
            query = RoleAssign.query.filter(
                RoleAssign.module == module,
                RoleAssign.user_code.isnot(None),
                RoleAssign.user_code != '',
            )

            if com_code:
                query = query.filter(RoleAssign.com_code == com_code)

            if key:
                query = query.filter(
                    or_(
                        RoleAssign.com_name.ilike(f'%{key}%'),
                        RoleAssign.user_name.ilike(f'%{key}%'),
                        RoleAssign.user_code.ilike(f'%{key}%')
                    )
                )

            pagination = query.paginate(page=page, per_page=limit, error_out=False)
            result = {
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': pagination.page,
                'per_page': pagination.per_page,
                'items': [item.to_dict() for item in pagination.items]
            }
            return success_response(result)
        except Exception as e:
            return fail_response(500, str(e))


@ns.route('/deleteRoleAssign')
class DeleteRoleAssign(Resource):
    """删除角色分配记录"""
    parser = reqparse.RequestParser()
    parser.add_argument('module', type=str, required=True, location='json', help='模块名称')
    parser.add_argument('com_code', type=str, required=True, location='json', help='企业代码')
    parser.add_argument('user_code', type=str, required=True, location='json', help='用户代码')

    @ns.expect(parser)
    def delete(self):
        """删除角色分配记录"""
        args = self.parser.parse_args()
        module = args['module']
        com_code = args['com_code']
        user_code = args['user_code']

        try:
            # 查找角色分配记录
            role_assign = RoleAssign.query.filter_by(
                module=module,
                com_code=com_code,
                user_code=user_code
            ).first()

            if not role_assign:
                return fail_response(404, "Role assign record not found")

            # 删除角色分配记录
            db.session.delete(role_assign)

            # 删除 UserRole 表中的相关记录
            UserRole.query.filter_by(username=user_code).delete()

            db.session.commit()
            return success_response("Role assign deleted successfully")
        except Exception as e:
            db.session.rollback()
            logger.error(e, exc_info=True)
            return fail_response(500, str(e))

@ns.route('/getEnterprisesByLevel')
class GetEnterprisesByLevel(Resource):
    """分页查询指定级别的企业列表，可以搜索"""
    parser = reqparse.RequestParser()
    parser.add_argument('com_level', type=str, required=False, location='args', help='企业层级')
    parser.add_argument('page', type=int, default=1, location='args', help='页码')
    parser.add_argument('limit', type=int, default=10, location='args', help='每页数量')
    parser.add_argument('search', type=str, location='args', help='搜索关键字')
    parser.add_argument('parent_code', type=str, location='args', help='父级组织代码')
    parser.add_argument('org_level', type=str, location='args', help='组织层级过滤，可选值：null, not_null')

    @ns.expect(parser)
    def get(self):
        """分页查询指定级别的企业列表，可以搜索"""
        args = self.parser.parse_args()
        com_level = args['com_level']
        page = args['page']
        limit = args['limit']
        search = args.get('search', '')
        parent_code = args.get('parent_code')
        org_level = args.get('org_level')

        try:
            # 构建过滤条件
            filters = {
                'search': search,
                'parent_code': parent_code,
                'include_virtual': False
            }
            
            # 处理组织层级过滤
            if org_level == 'null':
                filters['org_level_null'] = True
            elif org_level == 'not_null':
                filters['org_level_not_null'] = True
            
            # 获取基础查询
            query = Organization.get_base_query().filter(
                Organization.is_virtual_org == False,
                Organization.org_status == '1'
            )
            if com_level:
                query = query.filter(Organization.com_level == com_level)
            # 应用过滤条件
            if search:
                query = query.filter(or_(
                    Organization.name.like(f'%{search}%'),
                    Organization.fullname.like(f'%{search}%')
                ))
            
            if parent_code:
                query = query.filter(Organization.parent_org_code == parent_code)
                
            if org_level == 'null':
                query = query.filter(Organization.org_level.is_(None))
            elif org_level == 'not_null':
                query = query.filter(Organization.org_level.isnot(None))

            pagination = query.paginate(page=page, per_page=limit, error_out=False)
            result = {
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': pagination.page,
                'per_page': pagination.per_page,
                'items': [item.to_dict() for item in pagination.items]
            }
            return success_response(result)
        except Exception as e:
            logger.error(f"获取企业列表失败: {e}", exc_info=True)
            return fail_response(500, str(e))

@ns.route('/getAllTreeData')
class GetAllTreeData(Resource):
    """获取企业的树形结构数据 - 使用parent_org_code"""
    parser = reqparse.RequestParser()
    parser.add_argument('search', type=str, required=False, location='args', help='搜索关键字')
    parser.add_argument('includeDepartments', type=str, default='true', location='args', help='是否包含部门')
    parser.add_argument('includeVirtualOrg', type=str, default='true', location='args', help='是否包含虚拟组织')
    parser.add_argument('includeSubEnterprises', type=str, default='false', location='args', help='是否包含子企业')
    parser.add_argument('comLevel', type=str, default='', location='args', help='公司级别,多个值使用逗号分隔')
    parser.add_argument('parentCode', type=str, required=False, location='args', help='父级组织代码(parent_org_code)')
    
    @ns.expect(parser)
    def get(self):
        """获取企业的树形结构数据 - 使用parent_org_code字段"""
        args = self.parser.parse_args()
        
        # 修复参数处理逻辑
        search = args.get('search', '')
        include_departments_str = args.get('includeDepartments', 'true')
        include_virtual_str = args.get('includeVirtualOrg', 'true')
        include_sub_enterprises_str = args.get('includeSubEnterprises', 'false')
        com_level = args.get('comLevel', '')
        parent_code = args.get('parentCode', '')
        
        # 正确处理字符串 'None' 和布尔值转换
        if search and search.lower() == 'none':
            search = ''
        if parent_code and parent_code.lower() == 'none':
            parent_code = ''
        if com_level and com_level.lower() == 'none':
            com_level = ''
            
        # 布尔值转换，处理 'false', 'False', 'FALSE' 等情况
        include_departments = include_departments_str.lower() == 'true'
        include_virtual = include_virtual_str.lower() == 'true'
        include_sub_enterprises = include_sub_enterprises_str.lower() == 'true'
        
        # 确保空字符串转换为 None
        parent_code = parent_code if parent_code else None
        com_level = com_level if com_level else None
        search = search if search else None
        
        try:
            # 特殊业务逻辑：如果 parentCode 有值，并且 includeDepartments=false
            if parent_code and not include_departments:
                # 检查特殊条件并可能重定向到父节点的 com_level=03 节点
                redirect_parent_code = self._check_special_conditions(parent_code)
                if redirect_parent_code:
                    logger.info(f"特殊条件匹配，重定向到父节点: {redirect_parent_code}，查找 com_level=03 的节点")
                    # 修改过滤条件 - 注意这里仍然保持 include_departments=False
                    filters = {
                        'search': search,
                        'include_departments': False,  # 保持原始参数，不包含部门
                        'include_enterprises': True,
                        'com_level': '03',  # 只返回 com_level=03 的节点
                        'parent_code': redirect_parent_code,  # 使用父节点代码
                        'include_virtual_org': include_virtual
                    }
                    
                    tree_data = Organization.get_all_tree_data_optimized(**filters)
                    
                    # 进一步过滤，确保只返回企业类型的节点（com_level=03且org_level为空）
                    filtered_data = [
                        node for node in tree_data 
                        if node.get('type') == 'enterprise' and node.get('level') == '03'
                    ]
                    
                    logger.info(f"特殊逻辑返回 {len(filtered_data)} 个 com_level=03 的企业节点")
                    return success_response(filtered_data)
                
                # 检查虚拟父级条件：2级企业的父节点是虚拟一级企业
                virtual_parent_code = self._check_virtual_parent_conditions(parent_code)
                if virtual_parent_code:
                    logger.info(f"虚拟父级条件匹配，查询父节点 {virtual_parent_code} 下的所有企业")
                    # 查询父节点下除了目标节点本身的所有企业（除了com_level=04）
                    return self._get_virtual_parent_enterprises(virtual_parent_code, parent_code, search, include_virtual)
            
            # 新增逻辑：处理 includeSubEnterprises 参数
            if com_level and not parent_code and include_sub_enterprises:
                logger.info(f"includeSubEnterprises=true，获取 com_level={com_level} 企业下的所有子企业")
                result = self._get_enterprises_with_sub_enterprises(
                    com_level, search, include_departments, include_virtual
                )
                return success_response(result)
            
            # 正常的处理逻辑
            filters = {
                'search': search,
                'include_departments': include_departments,
                'include_enterprises': True,  # 默认包含企业
                'com_level': com_level,
                'parent_code': parent_code,
                'include_virtual_org': include_virtual
            }
            
            # 添加详细的调试信息
            logger.info(f"getAllTreeData 请求参数: search='{search}', parentCode='{parent_code}', comLevel='{com_level}', includeDepartments='{include_departments}', includeVirtualOrg='{include_virtual}', includeSubEnterprises='{include_sub_enterprises}'")
            logger.info(f"过滤条件: {filters}")
            
            tree_data = Organization.get_all_tree_data_optimized(**filters)
            
            # 添加结果统计
            logger.info(f"获取树形数据成功，返回 {len(tree_data)} 个节点")
            if tree_data:
                logger.debug(f"第一个节点: code={tree_data[0].get('code')}, name={tree_data[0].get('name')}, parent_org_code={tree_data[0].get('parent_org_code')}")
            
            return success_response(tree_data)
            
        except Exception as e:
            logger.error(f"获取树形数据失败: {e}", exc_info=True)
            return fail_response(500, str(e))
    
    def _check_special_conditions(self, parent_code):
        """
        检查特殊条件：
        1. 找到 code=parentCode 的节点
        2. 该节点的 com_level=02, is_virtual_org=false
        3. 该节点的父节点的 is_virtual_org=true, com_level=02
        如果满足条件，返回父节点的代码，否则返回 None
        """
        try:
            # 查找 parentCode 对应的节点
            target_node = Organization.query.filter_by(code=parent_code).first()
            
            if not target_node:
                logger.warning(f"未找到 code={parent_code} 的节点")
                return None
            
            logger.info(f"目标节点: code={target_node.code}, com_level={target_node.com_level}, is_virtual_org={target_node.is_virtual_org}, parent_org_code={target_node.parent_org_code}")
            
            # 检查目标节点条件：com_level=02, is_virtual_org=false
            if target_node.com_level != '02' or target_node.is_virtual_org != False:
                logger.info(f"目标节点不满足条件: com_level={target_node.com_level}, is_virtual_org={target_node.is_virtual_org}")
                return None
            
            # 查找父节点
            if not target_node.parent_org_code:
                logger.info("目标节点没有父节点")
                return None
            
            parent_node = Organization.query.filter_by(code=target_node.parent_org_code).first()
            
            if not parent_node:
                logger.warning(f"未找到父节点: {target_node.parent_org_code}")
                return None
            
            logger.info(f"父节点: code={parent_node.code}, com_level={parent_node.com_level}, is_virtual_org={parent_node.is_virtual_org}")
            
            # 检查父节点条件：is_virtual_org=true, com_level=02
            if parent_node.is_virtual_org != True or parent_node.com_level != '02':
                logger.info(f"父节点不满足条件: com_level={parent_node.com_level}, is_virtual_org={parent_node.is_virtual_org}")
                return None
            
            logger.info(f"所有条件满足，返回父节点代码: {parent_node.code}")
            return parent_node.code
            
        except Exception as e:
            logger.error(f"检查特殊条件时出错: {e}", exc_info=True)
            return None

    def _check_virtual_parent_conditions(self, parent_code):
        """
        检查虚拟父级条件：
        1. 找到 code=parentCode 的节点
        2. 该节点的 com_level=02
        3. 该节点的父节点的 is_virtual_org=true, com_level=01
        4. 父节点下没有其他 is_virtual_org=0, com_level=02的企业
        如果满足条件，返回父节点的代码，否则返回 None
        """
        try:
            # 查找 parentCode 对应的节点
            target_node = Organization.query.filter_by(code=parent_code).first()
            
            if not target_node:
                logger.warning(f"未找到 code={parent_code} 的节点")
                return None
            
            logger.info(f"虚拟父级检查 - 目标节点: code={target_node.code}, com_level={target_node.com_level}, is_virtual_org={target_node.is_virtual_org}, parent_org_code={target_node.parent_org_code}")
            
            # 检查目标节点条件：com_level=02
            if target_node.com_level != '02':
                logger.info(f"目标节点不是2级企业: com_level={target_node.com_level}")
                return None
            
            # 查找父节点
            if not target_node.parent_org_code:
                logger.info("目标节点没有父节点")
                return None
            
            parent_node = Organization.query.filter_by(code=target_node.parent_org_code).first()
            
            if not parent_node:
                logger.warning(f"未找到父节点: {target_node.parent_org_code}")
                return None
            
            logger.info(f"虚拟父级检查 - 父节点: code={parent_node.code}, com_level={parent_node.com_level}, is_virtual_org={parent_node.is_virtual_org}")
            
            # 检查父节点条件：is_virtual_org=true, com_level=01
            if parent_node.is_virtual_org != True or parent_node.com_level != '01':
                logger.info(f"父节点不满足虚拟一级企业条件: com_level={parent_node.com_level}, is_virtual_org={parent_node.is_virtual_org}")
                return None
            
            # 检查父节点下是否有其他 is_virtual_org=0, com_level=02的企业
            other_level2_enterprises = Organization.query.filter(
                Organization.parent_org_code == parent_node.code,
                Organization.com_level == '02',
                Organization.is_virtual_org == False,
                Organization.code != target_node.code,  # 排除目标节点本身
                Organization.org_status == '1',
                Organization.is_disabled == False
            ).first()
            
            if other_level2_enterprises:
                logger.info(f"父节点下存在其他2级非虚拟企业: {other_level2_enterprises.code}")
                return None
            
            logger.info(f"虚拟父级条件满足，返回父节点代码: {parent_node.code}")
            return parent_node.code
            
        except Exception as e:
            logger.error(f"检查虚拟父级条件时出错: {e}", exc_info=True)
            return None

    def _get_virtual_parent_enterprises(self, virtual_parent_code, exclude_code, search, include_virtual):
        """
        获取虚拟父节点下的分公司子企业
        优先查找同级的分公司，然后返回分公司下的子企业
        """
        try:
            # 先查找同级的分公司（com_level=02的企业）
            # 根据图片显示，50001723是"研究设计院集团分公司"
            branch_company = Organization.get_base_query().filter(
                Organization.parent_org_code == virtual_parent_code,
                Organization.com_level == '02',
                Organization.code != exclude_code,  # 排除目标节点本身
                Organization.org_level.is_(None),   # 确保是企业而不是部门
                Organization.org_status == '1'
            ).filter(
                or_(
                    Organization.fullname.like('%分公司%'),  # 通常分公司名称包含"分公司"
                    Organization.code == '50001723'  # 根据图片显示的具体代码
                )
            ).first()
            
            if branch_company:
                logger.info(f"找到分公司: {branch_company.code} - {branch_company.name}")
                
                # 获取父节点下的直接三级企业和分公司下的所有子企业
                all_enterprises = self._get_virtual_parent_all_enterprises(
                    virtual_parent_code, branch_company.code, exclude_code, search, include_virtual
                )
                
                logger.info(f"虚拟父级企业查询完成，返回 {len(all_enterprises)} 个企业节点")
                return success_response(all_enterprises)
            else:
                logger.info("未找到分公司，回退到查询父节点下的所有企业")
                # 如果没找到分公司，则查询父节点下的所有企业（原有逻辑）
                enterprises_query = Organization.get_base_query().filter(
                    Organization.parent_org_code == virtual_parent_code,
                    Organization.code != exclude_code,  # 排除目标节点本身
                    Organization.com_level != '04',     # 排除四级企业
                    Organization.org_level.is_(None),   # 确保是企业而不是部门
                    Organization.org_status == '1'
                )
                
                # 应用虚拟组织过滤
                if not include_virtual:
                    enterprises_query = enterprises_query.filter(Organization.is_virtual_org == False)
                
                # 应用搜索过滤
                if search:
                    enterprises_query = enterprises_query.filter(or_(
                        Organization.name.like(f'%{search}%'),
                        Organization.fullname.like(f'%{search}%'),
                        Organization.code.like(f'%{search}%')
                    ))
                
                enterprises = enterprises_query.all()
                logger.info(f"找到虚拟父节点 {virtual_parent_code} 下 {len(enterprises)} 个企业（排除 {exclude_code} 和 com_level=04）")
                
                # 构建结果数据
                result = []
                for enterprise in enterprises:
                    node = {
                        'name': enterprise.name,
                        'fullname': enterprise.fullname,
                        'code': enterprise.code,
                        'type': 'enterprise',
                        'parent_org_code': enterprise.parent_org_code,
                        '_parent': enterprise.parent_org_code,
                        'level': enterprise.com_level,
                        'children': [],
                        'is_virtual_org': enterprise.is_virtual_org or False
                    }
                    result.append(node)
                
                logger.info(f"虚拟父级企业查询完成，返回 {len(result)} 个企业节点")
                return success_response(result)
            
        except Exception as e:
            logger.error(f"获取虚拟父级企业失败: {e}", exc_info=True)
            return fail_response(500, str(e))

    def _get_all_sub_enterprises_recursive(self, parent_code, search, include_virtual, processed_codes):
        """
        递归获取所有子企业（保持树形结构）
        """
        try:
            # 查询直接子企业
            sub_enterprises_query = Organization.get_base_query().filter(
                Organization.parent_org_code == parent_code,
                Organization.com_level != '04',     # 排除四级企业
                Organization.org_level.is_(None),   # 确保是企业而不是部门
                Organization.org_status == '1'
            )
            
            # 应用虚拟组织过滤
            if not include_virtual:
                sub_enterprises_query = sub_enterprises_query.filter(Organization.is_virtual_org == False)
            
            # 应用搜索过滤
            if search:
                sub_enterprises_query = sub_enterprises_query.filter(or_(
                    Organization.name.like(f'%{search}%'),
                    Organization.fullname.like(f'%{search}%'),
                    Organization.code.like(f'%{search}%')
                ))
            
            sub_enterprises = sub_enterprises_query.all()
            logger.info(f"找到父节点 {parent_code} 下 {len(sub_enterprises)} 个直接子企业")
            
            result = []
            
            for enterprise in sub_enterprises:
                # 检查是否已处理过，避免循环引用
                if enterprise.code in processed_codes:
                    logger.warning(f"跳过已处理的企业: {enterprise.code}")
                    continue
                
                processed_codes.add(enterprise.code)
                
                # 递归查询当前企业的子企业
                children = self._get_all_sub_enterprises_recursive(
                    enterprise.code, search, include_virtual, processed_codes
                )
                
                # 构建当前企业节点（包含子企业）
                node = {
                    'name': enterprise.name,
                    'fullname': enterprise.fullname,
                    'code': enterprise.code,
                    'type': 'enterprise',
                    'parent_org_code': enterprise.parent_org_code,
                    '_parent': enterprise.parent_org_code,
                    'level': enterprise.com_level,
                    'children': children,  # 保持树形结构
                    'is_virtual_org': enterprise.is_virtual_org or False
                }
                
                result.append(node)
            
            logger.info(f"递归查询完成，父节点 {parent_code} 返回 {len(result)} 个直接子企业")
            return result
            
        except Exception as e:
            logger.error(f"递归获取子企业失败: {e}", exc_info=True)
            return []

    def _get_virtual_parent_all_enterprises(self, virtual_parent_code, branch_company_code, exclude_code, search, include_virtual):
        """
        获取虚拟父节点下的所有企业：
        1. 父节点下的直接三级企业
        2. 分公司下的所有子企业（树形结构）
        """
        try:
            result = []
            
            # 1. 获取父节点下的直接三级企业（排除目标节点和分公司）
            direct_enterprises_query = Organization.get_base_query().filter(
                Organization.parent_org_code == virtual_parent_code,
                Organization.com_level == '03',  # 只要三级企业
                Organization.code != exclude_code,  # 排除目标节点
                Organization.code != branch_company_code,  # 排除分公司
                Organization.org_level.is_(None),   # 确保是企业而不是部门
                Organization.org_status == '1'
            )
            
            # 应用虚拟组织过滤
            if not include_virtual:
                direct_enterprises_query = direct_enterprises_query.filter(Organization.is_virtual_org == False)
            
            # 应用搜索过滤
            if search:
                direct_enterprises_query = direct_enterprises_query.filter(or_(
                    Organization.name.like(f'%{search}%'),
                    Organization.fullname.like(f'%{search}%'),
                    Organization.code.like(f'%{search}%')
                ))
            
            direct_enterprises = direct_enterprises_query.all()
            logger.info(f"找到父节点 {virtual_parent_code} 下 {len(direct_enterprises)} 个直接三级企业")
            
            # 构建直接三级企业节点（递归查询其子企业）
            processed_codes = set()
            for enterprise in direct_enterprises:
                # 递归查询当前企业的子企业
                children = self._get_all_sub_enterprises_recursive(
                    enterprise.code, search, include_virtual, processed_codes
                )
                
                node = {
                    'name': enterprise.name,
                    'fullname': enterprise.fullname,
                    'code': enterprise.code,
                    'type': 'enterprise',
                    'parent_org_code': enterprise.parent_org_code,
                    '_parent': enterprise.parent_org_code,
                    'level': enterprise.com_level,
                    'children': children,  # 包含子企业的树形结构
                    'is_virtual_org': enterprise.is_virtual_org or False
                }
                result.append(node)
            
            # 2. 获取分公司下的所有子企业（树形结构）
            branch_sub_enterprises = self._get_all_sub_enterprises_recursive(
                branch_company_code, search, include_virtual, set()
            )
            
            logger.info(f"找到分公司 {branch_company_code} 下 {len(branch_sub_enterprises)} 个子企业")
            
            # 将分公司的子企业添加到结果中
            result.extend(branch_sub_enterprises)
            
            logger.info(f"合并完成，共返回 {len(result)} 个企业节点")
            return result
            
        except Exception as e:
            logger.error(f"获取虚拟父节点所有企业失败: {e}", exc_info=True)
            return []

    def _get_enterprises_with_sub_enterprises(self, com_level, search, include_departments, include_virtual):
        """
        获取指定级别企业下的所有子企业
        """
        try:
            # 1. 先获取指定级别的所有企业
            parent_enterprises_query = Organization.get_base_query().filter(
                Organization.com_level == com_level,
                Organization.org_level.is_(None),  # 确保是企业而不是部门
                Organization.org_status == '1'
            )
            
            # 应用虚拟组织过滤
            if not include_virtual:
                parent_enterprises_query = parent_enterprises_query.filter(Organization.is_virtual_org == False)
            
            # 应用搜索过滤
            if search:
                parent_enterprises_query = parent_enterprises_query.filter(or_(
                    Organization.name.like(f'%{search}%'),
                    Organization.fullname.like(f'%{search}%'),
                    Organization.code.like(f'%{search}%')
                ))
            
            parent_enterprises = parent_enterprises_query.all()
            logger.info(f"找到 {len(parent_enterprises)} 个 com_level={com_level} 的父企业")
            
            # 使用集合来跟踪已处理的组织，避免重复
            processed_codes = set()
            result = []
            
            # 2. 为每个父企业获取其子企业
            for parent_enterprise in parent_enterprises:
                if parent_enterprise.code in processed_codes:
                    continue  # 跳过已处理的企业
                
                processed_codes.add(parent_enterprise.code)
                
                # 构建父企业节点
                parent_node = {
                    'name': parent_enterprise.name,
                    'fullname': parent_enterprise.fullname,
                    'code': parent_enterprise.code,
                    'type': 'enterprise',
                    'parent_org_code': parent_enterprise.parent_org_code,
                    '_parent': parent_enterprise.parent_org_code,
                    'level': parent_enterprise.com_level,
                    'children': [],
                    'is_virtual_org': parent_enterprise.is_virtual_org or False
                }
                
                # 3. 查找子企业，传入已处理的代码集合
                sub_enterprises = self._get_sub_enterprises_recursive(
                    parent_enterprise.code, search, include_departments, include_virtual, processed_codes
                )
                parent_node['children'] = sub_enterprises
                
                result.append(parent_node)
            
            # 4. 特殊处理：当查询二级企业时，也要包含那些父级是一级企业的三级企业
            if com_level == '02':
                logger.info("检查是否有父级是一级企业的三级企业")
                self._add_level3_enterprises_under_level1(result, search, include_departments, include_virtual, processed_codes)
            
            logger.info(f"获取企业及子企业完成，返回 {len(result)} 个根节点，已处理 {len(processed_codes)} 个组织")
            return result
            
        except Exception as e:
            logger.error(f"获取企业及子企业失败: {e}", exc_info=True)
            return []
    
    def _add_level3_enterprises_under_level1(self, result, search, include_departments, include_virtual, processed_codes):
        """
        添加那些父级是一级企业的三级企业
        """
        try:
            # 查找所有一级企业
            level1_enterprises = Organization.get_base_query().filter(
                Organization.com_level == '01',
                Organization.org_level.is_(None),
                Organization.org_status == '1'
            ).all()
            
            if not include_virtual:
                level1_enterprises = [org for org in level1_enterprises if not org.is_virtual_org]
            
            logger.info(f"找到 {len(level1_enterprises)} 个一级企业")
            
            for level1_enterprise in level1_enterprises:
                if level1_enterprise.code in processed_codes:
                    continue
                
                # 查找直接隶属于一级企业的三级企业
                level3_enterprises_query = Organization.get_base_query().filter(
                    Organization.parent_org_code == level1_enterprise.code,
                    Organization.com_level == '03',
                    Organization.org_level.is_(None),
                    Organization.org_status == '1'
                )
                
                # 应用虚拟组织过滤
                if not include_virtual:
                    level3_enterprises_query = level3_enterprises_query.filter(Organization.is_virtual_org == False)
                
                # 应用搜索过滤
                if search:
                    level3_enterprises_query = level3_enterprises_query.filter(or_(
                        Organization.name.like(f'%{search}%'),
                        Organization.fullname.like(f'%{search}%'),
                        Organization.code.like(f'%{search}%')
                    ))
                
                level3_enterprises = level3_enterprises_query.all()
                
                if level3_enterprises:
                    logger.info(f"一级企业 {level1_enterprise.name} 下找到 {len(level3_enterprises)} 个直属三级企业")
                    
                    # 为一级企业创建节点（作为二级企业的平级节点）
                    processed_codes.add(level1_enterprise.code)
                    
                    level1_node = {
                        'name': level1_enterprise.name,
                        'fullname': level1_enterprise.fullname,
                        'code': level1_enterprise.code,
                        'type': 'enterprise',
                        'parent_org_code': level1_enterprise.parent_org_code,
                        '_parent': level1_enterprise.parent_org_code,
                        'level': level1_enterprise.com_level,
                        'children': [],
                        'is_virtual_org': level1_enterprise.is_virtual_org or False
                    }
                    
                    # 添加三级企业作为一级企业的子节点
                    for level3_enterprise in level3_enterprises:
                        if level3_enterprise.code in processed_codes:
                            continue
                        
                        processed_codes.add(level3_enterprise.code)
                        
                        level3_node = {
                            'name': level3_enterprise.name,
                            'fullname': level3_enterprise.fullname,
                            'code': level3_enterprise.code,
                            'type': 'enterprise',
                            'parent_org_code': level3_enterprise.parent_org_code,
                            '_parent': level3_enterprise.parent_org_code,
                            'level': level3_enterprise.com_level,
                            'children': [],
                            'is_virtual_org': level3_enterprise.is_virtual_org or False
                        }
                        
                        # 递归获取三级企业的子节点
                        children = self._get_sub_enterprises_recursive(
                            level3_enterprise.code, search, include_departments, include_virtual, processed_codes
                        )
                        level3_node['children'] = children
                        
                        level1_node['children'].append(level3_node)
                    
                    # 将一级企业节点添加到结果中
                    result.append(level1_node)
        
        except Exception as e:
            logger.error(f"添加一级企业下的三级企业失败: {e}", exc_info=True)

    def _get_sub_enterprises_recursive(self, parent_code, search, include_departments, include_virtual, processed_codes=None):
        """
        递归获取子企业
        """
        if processed_codes is None:
            processed_codes = set()
            
        try:
            # 查询直接子企业
            sub_enterprises_query = Organization.get_base_query().filter(
                Organization.parent_org_code == parent_code,
                Organization.org_status == '1'
            )
            
            # 应用虚拟组织过滤
            if not include_virtual:
                sub_enterprises_query = sub_enterprises_query.filter(Organization.is_virtual_org == False)
            
            # 应用搜索过滤
            if search:
                sub_enterprises_query = sub_enterprises_query.filter(or_(
                    Organization.name.like(f'%{search}%'),
                    Organization.fullname.like(f'%{search}%'),
                    Organization.code.like(f'%{search}%')
                ))
            
            sub_enterprises = sub_enterprises_query.all()
            
            result = []
            for sub_enterprise in sub_enterprises:
                # 检查是否已处理过此组织，避免重复
                if sub_enterprise.code in processed_codes:
                    logger.debug(f"跳过重复组织: {sub_enterprise.code} - {sub_enterprise.name}")
                    continue
                
                # 根据 includeDepartments 参数过滤
                if not include_departments and sub_enterprise.org_level:
                    continue  # 跳过部门
                
                # 将当前组织标记为已处理
                processed_codes.add(sub_enterprise.code)
                
                node = {
                    'name': sub_enterprise.name,
                    'fullname': sub_enterprise.fullname,
                    'code': sub_enterprise.code,
                    'type': 'enterprise' if not sub_enterprise.org_level else 'department',
                    'parent_org_code': sub_enterprise.parent_org_code,
                    '_parent': sub_enterprise.parent_org_code,
                    'level': sub_enterprise.org_level or sub_enterprise.com_level,
                    'children': [],
                    'is_virtual_org': sub_enterprise.is_virtual_org or False
                }
                
                # 递归获取子节点
                children = self._get_sub_enterprises_recursive(
                    sub_enterprise.code, search, include_departments, include_virtual, processed_codes
                )
                node['children'] = children
                
                result.append(node)
            
            return result
            
        except Exception as e:
            logger.error(f"递归获取子企业失败: {e}", exc_info=True)
            return []

@ns.route('/getOrganizationDetail')
class GetOrganizationDetail(Resource):
    """获取组织的详细信息，包括父节点信息"""
    parser = reqparse.RequestParser()
    parser.add_argument('enterprise_code', type=str, required=True, location='args', help='企业代码')

    @ns.expect(parser)
    def get(self):
        """获取组织的详细信息，包括父节点信息"""
        args = self.parser.parse_args()
        enterprise_code = args['enterprise_code']

        try:
            # 获取组织基本信息
            organization = Organization.query.filter_by(code=enterprise_code).first()
            if not organization:
                return fail_response(404, "组织不存在")

            # 获取组织信息并转换为字典
            result = organization.to_dict()
            
            # 初始化父节点信息
            result['parent_node'] = {}
            result['virtual_parent_node'] = {}

            # 如果是三级企业
            if organization.com_level == '03':
                # 1. 先获取直接父节点（可能是虚拟组织）
                if organization.parent_org_code:
                    direct_parent = Organization.query.filter_by(code=organization.parent_org_code).first()
                    if direct_parent:
                        # 如果直接父节点是虚拟组织，将其放入 virtual_parent_node
                        if direct_parent.is_virtual_org:
                            result['virtual_parent_node'] = direct_parent.to_dict()
                            # 继续查找真实的父节点（非虚拟组织）
                            real_parent = Organization.query.filter(
                                Organization.com_level == '02',
                                Organization.is_virtual_org == False,
                                Organization.parent_org_code == direct_parent.code
                            ).first()
                            if real_parent:
                                result['parent_node'] = real_parent.to_dict()
                        else:
                            # 如果直接父节点不是虚拟组织，直接作为 parent_node
                            result['parent_node'] = direct_parent.to_dict()
                
                # 2. 如果还没有找到 parent_node，需要继续往上查询
                if not result['parent_node'] and organization.parent_org_code:
                    parent_node = self._find_real_parent_node(organization.parent_org_code)
                    if parent_node:
                        result['parent_node'] = parent_node.to_dict()
            else:
                # 非三级企业，直接获取父节点
                if organization.parent_org_code:
                    parent_org = Organization.query.filter_by(code=organization.parent_org_code).first()
                    if parent_org:
                        result['parent_node'] = parent_org.to_dict()

            return success_response(result)
        except Exception as e:
            logger.error(f"获取组织详情失败: {e}", exc_info=True)
            return fail_response(500, str(e))

    def _find_real_parent_node(self, current_parent_code):
        """
        递归查找真实的父节点
        处理 com_level=03 且 is_virtual_org=1 的情况
        """
        if not current_parent_code:
            return None
        
        current_parent = Organization.query.filter_by(code=current_parent_code).first()
        if not current_parent:
            return None
        
        # 如果当前父节点是 com_level=02 且非虚拟组织，直接返回
        if current_parent.com_level == '02' and not current_parent.is_virtual_org:
            return current_parent
        
        # 如果当前父节点是 com_level=03 且是虚拟组织，继续往上查询
        if current_parent.com_level == '03' and current_parent.is_virtual_org:
            # 查找当前父节点的父节点
            if current_parent.parent_org_code:
                grand_parent = Organization.query.filter_by(code=current_parent.parent_org_code).first()
                if grand_parent and grand_parent.is_virtual_org:
                    # 如果祖父节点也是虚拟组织，查找其下是否有 com_level=02 且 is_virtual_org=0 的节点
                    level2_non_virtual = Organization.query.filter(
                        Organization.com_level == '02',
                        Organization.is_virtual_org == False,
                        Organization.parent_org_code == grand_parent.code
                    ).first()
                    if level2_non_virtual:
                        return level2_non_virtual
                    else:
                        # 继续递归查询祖父节点的父节点
                        return self._find_real_parent_node(grand_parent.code)
        
        # 如果当前父节点是 com_level=02 且是虚拟组织，查找其下是否有 com_level=02 且 is_virtual_org=0 的节点
        if current_parent.com_level == '02' and current_parent.is_virtual_org:
            level2_non_virtual = Organization.query.filter(
                Organization.com_level == '02',
                Organization.is_virtual_org == False,
                Organization.parent_org_code == current_parent.code
            ).first()
            if level2_non_virtual:
                return level2_non_virtual
        
        # 如果都不满足条件，继续往上查询
        if current_parent.parent_org_code:
            return self._find_real_parent_node(current_parent.parent_org_code)
        
        return None

