# from .user_routes import bp as user_bp
#
# DEFAULT_BLUEPRINT = [
#     (user_bp, '/api/user/'),
# ]
#
# def config_blueprint(app):
#     for blueprint, prefix in DEFAULT_BLUEPRINT:
#         app.register_blueprint(blueprint, url_prefix=prefix)

from app.routes.user_routes import ns as user_ns
from app.routes.organization_routes import ns as organization_ns
from app.routes.role_routes import ns as role_ns
from app.routes.permission_routes import ns as permission_ns
from app.routes.category_routes import ns as category_ns
from app.routes.resource_routes import ns as resource_ns
from app.routes.rbac_routes import ns as rbac_ns
DEFAULT_NAMESPACES = [
    (user_ns, '/wolf/user'),  
    (organization_ns, '/wolf/organization'), 
    (role_ns, '/wolf/role'), 
    (permission_ns, '/wolf/permission'), 
    (category_ns, '/wolf/category'), 
    (resource_ns, '/wolf/resource'), 
    (rbac_ns, '/wolf/rbac'), 
]
from flask_restx import Api

def config_namespaces(api: Api):
    """
    注册所有命名空间
    :param api: Flask-RESTx 的 Api 实例
    """
    for namespace, prefix in DEFAULT_NAMESPACES:
        api.add_namespace(namespace, path=prefix)