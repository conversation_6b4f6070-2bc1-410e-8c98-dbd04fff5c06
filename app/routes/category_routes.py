import logging
from flask_restx import Namespace, Resource, reqparse
from sqlalchemy import or_
from app.models.category import Category as CategoryModel
from app.models.permission import Permission as PermissionModel
from app.utils.response import success_response, fail_response
from app.utils.errors import ArgsError

logger = logging.getLogger("flask_app")
ns = Namespace("categories", description="Category Management")

# 请求解析器：用于列表查询
list_parser = reqparse.RequestParser()
# list_parser.add_argument('appID', type=str, required=True, help='App ID')
list_parser.add_argument('limit', type=int, default=10, help='Number of items per page')
list_parser.add_argument('page', type=int, default=1, help='Page number')
list_parser.add_argument('key', type=str, help='Search key')
list_parser.add_argument('order', type=str, default='-id', help='Ordering')

# 请求解析器：用于创建和更新分类
create_update_parser = reqparse.RequestParser()
# create_update_parser.add_argument('appID', type=str, required=True, help='App ID')
create_update_parser.add_argument('name', type=str, required=True, help='Category name')


@ns.route('/')
class CategoryListResource(Resource):
    """Resource for category list and creation"""

    @ns.expect(list_parser)
    def get(self):
        """
        GET: Fetch categories list
        """
        args = list_parser.parse_args()
        limit = args["limit"]
        page = args["page"]
        offset = (page - 1) * limit
        key = args.get("key")
        order = args.get("order")

        query = CategoryModel.get_query()

        if key:
            query = query.filter(or_(
                CategoryModel.name.like(f"%{key}%")
            ))

        if order.startswith("-"):
            query = query.order_by(getattr(CategoryModel, order[1:]).desc())
        else:
            query = query.order_by(getattr(CategoryModel, order).asc())

        total = query.count()
        categories = query.offset(offset).limit(limit).all()
        data = {
            "categories": [category.to_dict() for category in categories],
            "total": total
        }
        return success_response(data)

    @ns.expect(create_update_parser)
    def post(self):
        """
        POST: Create a new category
        """
        args = create_update_parser.parse_args()
        name = args["name"]

        # Check for duplicates
        if CategoryModel.query.filter_by(name=name).first():
            return fail_response(400, "分类名称已存在")

        # 创建分类
        new_category = CategoryModel(name=name)
        new_category.save()
        return success_response({"category": new_category.to_dict()})


@ns.route('/<string:id>/')
class CategoryDetailResource(Resource):
    """Resource for single category operations"""

    def get(self, id):
        """
        GET: Fetch a single category by ID
        """
        category = CategoryModel.get_by_id(id)
        if not category:
            return fail_response(404, "分类不存在")
        return success_response({"category": category.to_dict()})

    @ns.expect(create_update_parser)
    def put(self, id):
        """
        PUT: Update a category by ID
        """
        args = create_update_parser.parse_args()
        name = args["name"]
        category = CategoryModel.get_by_id(id)
        if not category:
            return fail_response(404, "分类不存在")
        category.name = name
        category.save()
        return success_response({"category": category.to_dict()})

    def delete(self, id):
        """
        DELETE: Delete a category by ID
        """
        category = CategoryModel.get_by_id(id)
        if not category:
            return fail_response(404, "分类不存在")
        # 检查是否被使用
        in_use = PermissionModel.get_query().filter_by(category_id=id).first()
        if in_use:
            logger.error(f"Deleting the category({id}) failed, it has been used.")
            raise ArgsError("有关联的权限，分类不能删除")
        category.soft_delete()
        return success_response()
