import base64
from datetime import datetime
import json
import logging
from flask import Response, g, jsonify, redirect, request
from flask_restx import Namespace, Resource, fields,reqparse
from sqlalchemy import or_, text, and_
from app.config import Config, ZhuyunOauthConfig
from app.models.common import generate_model_from_sqlalchemy
from app.models.user_role import UserRole
from app.service.oauth import OauthService
from app.service.user import UserService
from app.models.user import User
from app.utils.args_helper import ArgsHelper
from app.utils.common import config_to_dict
from app.utils.redis_utils import UserRedisUtils
from app.utils.response import fail_response, success_response
from app.extension import db
from flask import current_app as app
from flask_jwt_extended import jwt_required

logger = logging.getLogger('flask_app')
# 定义命名空间
ns = Namespace('users', description='User related operations')

# 定义登录和用户信息的模型
login_model = ns.model('Login', {
    'username': fields.String(required=True, description='User login name'),
    'password': fields.String(required=True, description='User password'),
})

user_info_model = ns.model('UserInfo', {
    'id': fields.Integer(description='User ID'),
    'username': fields.String(description='User login name'),
    'email': fields.String(description='User email address'),
    'roles': fields.List(fields.String, description='User roles'),
})


@ns.route('/login')
class Login(Resource):
    @ns.expect(login_model)
    def post(self):
        """用户登录"""
        username = ArgsHelper.get_required_string_arg('username')
        password = ArgsHelper.get_required_string_arg('password')
        return UserService.login(username, password)


@ns.route('/logout')
class Logout(Resource):
    def get(self):
        """用户注销"""
        UserService.logout()
        return success_response('注销成功')


@ns.route('/info')
class UserInfo(Resource):
    # @ns.marshal_with(user_info_model)
    def get(self):
        """获取用户信息"""
        return UserService.info()

user_info_model = generate_model_from_sqlalchemy(
    namespace=ns,
    model=User,
    excluded_fields=['password', 'tel', 'email']  # 排除敏感字段
)

@ns.route('/list')
class UserList(Resource):
    parser = reqparse.RequestParser()
    parser.add_argument('limit', type=int, default=10, help='Number of users to retrieve')
    parser.add_argument('page', type=int, default=1, help='Page number')
    parser.add_argument('search', type=str, help='Search key')
    parser.add_argument('code', type=str, help='Code filter')
    parser.add_argument('dep_code', type=str, help='Department code filter')
    parser.add_argument('com_level', type=str, help='Company level filter, multiple values separated by comma')
    parser.add_argument('filter_disabled', type=lambda x: x.lower() in ['true', '1', 'yes'], default=False, help='Whether to filter disabled users (is_disabled=1, is_locked=1, status=0)')
    
    @ns.expect(parser)
    def get(self):
        """获取用户列表"""
        args = self.parser.parse_args()
        logger.info(f"Pagination params: page={args.page}, limit={args.limit}")
        return UserService.get_user_list(**args)
        


@ns.route('/getZhuyunLoginUrl')
class LoginUrl(Resource):
    @ns.response(200, '成功', model=ns.model('ZhuyunLoginUrl', {
        'url': fields.String
    }))
    def get(self):
        """获取竹云登录url"""
        url = UserService.get_login_url()
        return success_response({"url": url})

@ns.route('/loginCallback')
class LoginCallback(Resource):
      def get(self):
        """竹云登录回调"""
        # 获取 code 和 state 参数
        code = request.args.get('code')
        state = request.args.get('state')

        logger.info(f"code: {code}")
        logger.info(f"state: {state}")

        if not code:
            return fail_response(400, "无效的code")
        if not state:
            return fail_response(400, "无效的state")

        # 解码 state 参数
        try:
            # 计算需要的填充
            state = state + '=' * (-len(state) % 4)  # 计算需要添加的填充字符
            logger.debug(f"padded state: {state}")
            decoded_state = base64.b64decode(state).decode('utf-8')
            decoded_state = json.loads(decoded_state)
        except Exception as e:
            logger.error(f"State 解码失败: {e}")
            logger.error(f"State Is: {state}")
            return fail_response(400, "state 解码失败")

        logger.info(f"decode state: {decoded_state}")

        # 调用 OAuthService 获取用户信息
        zhuyun_config = config_to_dict(ZhuyunOauthConfig)
        service = OauthService(code, zhuyun_config)  # ZHUYUN_OAUTH 是对应的 OAuth 配置
        user_info = service.get_user_info()
        if not user_info or "loginName" not in user_info:
            return fail_response(400, "用户信息获取失败", user_info)
        logger.info(f"user_info: {user_info}")
        # 查询用户信息
        username = user_info.get("loginName")
        user = User.query.filter_by(username=username).first()

        if user:
            token, roles = UserService.create_token(user)
            userInfo = user.to_dict()
            userInfo['role_ids'] = roles
            logger.info(f"username: {user.username}, token: {token}")
            logger.debug(f"userInfo: {userInfo}")
            result = UserRedisUtils.set_userinfo(user.username, json.dumps(userInfo))
            if not result:
                logger.error("redis set key error")
                return fail_response(500, "redis set key error")
            # result = redis_utils.set_key(f"user:{user.username}:token", token, ex=60 * 60 * 24)
            result = UserRedisUtils.set_user_token(user.username, token)
            if not result:
                logger.error("redis set key error")
                return fail_response(500, "redis set key error")    
            # 重定向到前端指定的登录地址
            login_redirect_url = decoded_state.get("loginRedirectUrl", "")
            return redirect(f"{login_redirect_url}?token={token}&state={state}")
        else:
            return fail_response(400, f"没有获取到用户: {username}")



@ns.route('/getUsersByComCode')
class GetUsersByComCode(Resource):
    # 定义请求参数解析器
    parser = ns.parser()
    parser.add_argument('key', type=str, required=False, location='args', help='关键字搜索')
    parser.add_argument('comCode', type=str, required=True, location='args', help='企业代码不能为空')
    parser.add_argument('roles', type=str, action='split', required=False, location='args', help='角色列表')
    parser.add_argument('order', type=str, default='-username', location='args', help='排序字段')

    @ns.expect(parser)  # 将参数解析器绑定到当前接口
    def get(self):
        """通过企业代码查询人员列表"""
        args = self.parser.parse_args()
        com_code = args.get('comCode')
        if not com_code:
            return fail_response(400, "参数错误，comCode不能为空")
        users = UserService.getUsersByComCode(**args)
        return success_response(users)

# 定义模型（用于Swagger展示响应结构）
user_model = ns.model('User', {
    "id": fields.Integer(description="用户ID"),
    "code": fields.String(description="用户名"),
    "user_name": fields.String(description="用户姓名"),
    "com_code": fields.String(description="公司代码"),
    "com_name": fields.String(description="公司名称"),
    "dep_name": fields.String(description="部门名称"),
    "dep_code": fields.String(description="部门代码"),
    "com_level": fields.String(description="公司级别"),
    "tel": fields.String(description="电话"),
    "email": fields.String(description="电子邮件")
})

count_model = ns.model('Count', {
    "count": fields.Integer(description="统计数量")
})

url_model = ns.model('Url', {
    "url": fields.String(description="登出URL")
})


@ns.route('/getLogoutUrl')
class GetLogoutUrl(Resource):
    @ns.response(200, "请求成功", model=ns.model('getLogoutUrl', {
        "url": fields.String(description="登出URL")
    }))
    def get(self):
        """获取竹云登出URL"""
        return success_response({"url": ZhuyunOauthConfig.LOGOUT_URL})


@ns.route('/getUserCount')
class GetUserCount(Resource):
    parser = reqparse.RequestParser()
    parser.add_argument('comCode', type=str, required=False, location='args', help='企业代码')

    @ns.expect(parser)
    @ns.response(200, "请求成功", model=ns.model('getUserCount', {
        'count': fields.Integer(description='用户数量')
    }))
    def get(self):
        """获取用户数量"""
        args = self.parser.parse_args()
        com_code = args['comCode']

        query = User.query.filter(User.is_disabled == False, User.status == 1)
        if com_code:
            query = query.filter(User.com_code == com_code)
        count = query.count()
        return success_response({"count": count})


@ns.route('/bindRoles')
class BindRoles(Resource):
    parser = reqparse.RequestParser()
    parser.add_argument('action', type=str, default='add', location='json', help='操作类型: add 或 update')
    parser.add_argument('code', type=str, required=True, location='json', help='用户名')
    parser.add_argument('roleIds', type=list, required=True, location='json', help='角色ID列表')

    @ns.expect(parser)
    def post(self):
        """绑定用户角色"""
        args = self.parser.parse_args()
        action = args['action']
        username = args['code']
        role_ids = args['roleIds']

        # 验证用户是否存在
        user = User.query.filter_by(username=username).first()
        if not user:
            return fail_response(400, f"用户 {username} 不存在")

        try:
            # 如果是 update 操作，先删除该用户所有的角色
            if action == 'update':
                UserRole.query.filter_by(username=username).delete()

            # 添加角色记录，避免重复添加
            existing_roles = {ur.role_id for ur in UserRole.query.filter_by(username=username).all()}
            new_roles = set(role_ids) - existing_roles

            for role_id in new_roles:
                user_role = UserRole(
                    username=username,
                    role_id=role_id
                )
                db.session.add(user_role)

            db.session.commit()

            # 返回绑定的角色列表
            updated_roles = [ur.role_id for ur in UserRole.query.filter_by(username=username).all()]
            return success_response({
                "username": username,
                "role_ids": updated_roles,
                "update_time": datetime.utcnow().isoformat()
            })

        except Exception as e:
            db.session.rollback()
            return fail_response(500, f"绑定角色失败: {str(e)}")

# 接口实现
@ns.route('/getUsersByRoleIds')
class GetUsersByRoleIds(Resource):
    # 定义响应字段
    user_response_fields = {
        "id": fields.Integer(description="用户ID"),
        "code": fields.String(description="用户名"),
        "user_name": fields.String(description="用户姓名"),
        "com_code": fields.String(description="公司代码"),
        "com_name": fields.String(description="公司名称"),
        "dep_name": fields.String(description="部门名称"),
        "dep_code": fields.String(description="部门代码"),
        "com_level": fields.String(description="公司级别"),
        "tel": fields.String(description="电话"),
        "email": fields.String(description="电子邮件")
    }
    # 参数解析器
    parser = reqparse.RequestParser()
    parser.add_argument('roles', type=str, required=True, location='args', help='角色ID列表,逗号分隔')
    parser.add_argument('comCode', type=str, location='args', help='企业id')

    @ns.expect(parser)
    @ns.response(200, '成功', model=ns.model('getUsersByRoleIds', user_response_fields))  # 使用 @ns.response 定义响应
    def get(self):
        """根据角色ID获取用户列表"""
        args = self.parser.parse_args()
        role_ids = args['roles']
        com_code = args['comCode']
        role_ids = role_ids.split(',')
        # 查询逻辑：关联 UserRole 过滤角色 ID
        users_query = (
            User.get_query()
            .join(UserRole, User.username == UserRole.username)
            .filter(UserRole.role_id.in_(role_ids))
            .filter(and_(User.is_disabled == False, User.status == 1, User.is_locked == False))
        )
        if com_code:
            users_query = users_query.filter(User.com_code == com_code)

        # 分页查询
        users = users_query.all()
        # 数据整理
        result = [
            {
                "id": user.id,
                "code": user.username,
                "user_name": user.nickname,
                "com_code": user.com_code,
                "com_name": user.com_name,
                "dep_name": user.dep_name,
                "dep_code": user.dep_code,
                "com_level": user.com_level,
                "tel": user.tel,
                "email": user.email
            }
            for user in users
        ]
        logger.info(f"result: {result}")
        return success_response(result)

@ns.route('/removeUserRoles')
class RemoveUserRoles(Resource):
    # 定义参数解析器
    parser = reqparse.RequestParser()
    parser.add_argument('role_ids', type=list, location='json', required=True, help='角色ID列表，必填')
    parser.add_argument('username', type=str, location='json', required=True, help='用户名，必填')

    @ns.expect(parser)
    def post(self):
        """移除用户角色"""
        args = self.parser.parse_args()
        role_ids = args['role_ids']
        username = args['username']

        # 校验用户是否存在
        user = User.query.filter_by(username=username).first()
        if not user:
            return fail_response(400, f"用户 {username} 不存在")

        # 查询对应的用户角色记录
        user_roles = UserRole.query.filter(UserRole.username == username, UserRole.role_id.in_(role_ids)).all()
        if not user_roles:
            return fail_response(400, f"用户 {username} 的指定角色不存在")

        # 删除对应的用户角色记录
        for user_role in user_roles:
            db.session.delete(user_role)

        db.session.commit()
        return success_response()

@ns.route('/getUserInfo')
class GetUserInfo(Resource):
    """获取用户信息"""
    def get(self):
        return success_response(g.user.to_dict())




# 定义更新用户信息的模型
update_user_model = ns.model('UpdateUser', {
    'nickname': fields.String(description='用户昵称'),
    'email': fields.String(description='用户电子邮件'),
    'tel': fields.String(description='用户电话'),
    'fullname': fields.String(description='用户全名'),
    'sex': fields.Integer(description='用户性别'),
    'birthday': fields.String(description='用户生日, 格式 YYYY-MM-DD'),
    'dep_level': fields.String(description='部门级别'),
    'dep_name': fields.String(description='部门名称'),
    'dep_code': fields.String(description='部门编码'),
    'com_level': fields.String(description='公司级别'),
    'com_name': fields.String(description='公司名称'),
    'com_code': fields.String(description='公司编码'),
    'job_name': fields.String(description='职位名称'),
    'job_code': fields.String(description='职位编码'),
    'office_phone': fields.String(description='办公电话'),
    'identity_card': fields.String(description='身份证号码'),
    'status': fields.String(description="用户状态：'0'-启用 '-1'-禁用")
})
    

@ns.route('/<string:username>/')
class UserDetail(Resource):
    delete_response_model = ns.model('DeleteUserResponse', {
        'message': fields.String(description='删除结果消息')
    })

    @ns.response(200, '成功', delete_response_model)
    @ns.response(404, '用户不存在')
    @ns.response(403, '没有权限删除用户')
    @ns.response(500, '服务器错误')
    def delete(self, username):
        """通过用户名删除用户"""
        current_user = g.user  # 从全局 g 对象获取当前用户
        if username in ['admin', 'root']:
            return fail_response(403, "当前用户不能删除")
        if current_user.manager != 'super':
            return fail_response(403, "没有权限删除用户")
        return UserService.delete_user_by_username(username)

    
    @ns.expect(update_user_model)
    @ns.response(200, '更新成功', model=user_model)
    @ns.response(400, '请求参数错误')
    @ns.response(404, '用户不存在')
    @ns.response(403, '没有权限更新用户')
    @ns.response(500, '服务器错误')
    def put(self, username):
        """通过用户名更新用户信息"""
        current_user = g.user  # 从全局 g 对象获取当前用户
        if current_user.manager != 'super':
            return fail_response(403, "没有权限更新用户")
        data = request.json
        return UserService.update_user_by_username(username, data)

create_user_model = ns.model('CreateUser', {
    'username': fields.String(required=True, description='用户名'),
    'nickname': fields.String(required=True, description='用户昵称'),
    'com_code': fields.String(required=True, description='公司编码'),
    'manager': fields.String(required=True, description='管理员类型'),
    'status': fields.Integer(required=True, description='用户状态'),
})
@ns.route('')
class CreateUser(Resource):
    @ns.expect(create_user_model)
    @ns.response(201, '创建成功', model=user_model)
    @ns.response(400, '请求参数错误')
    @ns.response(403, '没有权限创建用户')
    @ns.response(409, '用户名已存在')
    @ns.response(500, '服务器错误')
    def post(self):
        """创建用户"""
        data = request.json
        return UserService.create_user(data)

# 定义设置角色的请求模型
set_roles_model = ns.model('SetRoles', {
    'username': fields.String(required=True, description='用户名'),
    'role_ids': fields.List(fields.String, required=True, description='角色ID列表')
})

# 定义设置角色的响应模型
set_roles_response_model = ns.model('SetRolesResponse', {
    'message': fields.String(description='设置结果消息'),
    'user_info': fields.Nested(ns.model('UserInfo', {
        "id": fields.Integer(description="用户ID"),
        "username": fields.String(description="用户名"),
        "nickname": fields.String(description="用户昵称"),
        "email": fields.String(description="电子邮件"),
        "roles": fields.List(fields.String, description="用户角色列表")
    }))
})

@ns.route('/set-roles/')
class SetRoles(Resource):
    @ns.expect(set_roles_model)
    @ns.response(200, '设置成功', set_roles_response_model)
    @ns.response(400, '请求参数错误')
    @ns.response(404, '用户不存在')
    @ns.response(403, '没有权限设置角色')
    @ns.response(500, '服务器错误')
    def post(self):
        """设置用户角色"""
        current_user = g.user  # 从全局 g 对象获取当前用户
        if current_user.manager != 'super':
            return fail_response(403, "没有权限设置角色")
        
        data = request.json
        username = data.get('username')
        role_ids = data.get('role_ids')

        if not username or not isinstance(role_ids, list):
            return fail_response(400, "请求参数错误，必须包含username和role_ids")
        
        return UserService.set_roles_for_user(username, role_ids)
