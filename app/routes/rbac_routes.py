import base64
from datetime import datetime
import json
import logging
from flask import Response, g, jsonify, redirect, request
from flask_restx import Namespace, Resource, fields,reqparse
from sqlalchemy import or_
from app.config import ZhuyunOauthConfig
from app.models.common import generate_model_from_sqlalchemy
from app.models.user_role import UserRole
from app.service.oauth import OauthService
from app.service.resource import ResourceService
from app.service.user import UserService
from app.models.user import User
from app.utils.args_helper import ArgsHelper
from app.utils.common import config_to_dict
from app.utils.redis_utils import UserRedisUtils
from app.utils.response import fail_response, success_response
from app.extension import db
from app.models.resource import Resource as ResourceModel

logger = logging.getLogger('flask_app')
# 定义命名空间
ns = Namespace('rbac', description='Rbac related operations')

@ns.route('/access_check')
class AccessCheck(Resource):
    parser = reqparse.RequestParser()
    parser.add_argument('resName', type=str, help='resName', required=True)
    parser.add_argument('action', type=str, help='action', required=True)
    @ns.expect(parser)
    def get(self):
        args = self.parser.parse_args()
        resource_name = args.get('resName')
        action = args.get('action')
        try:
            return ResourceService.check_permission(resource_name, action)
        except Exception as e:
            logger.error(f"AccessCheck error: {e}") 
            return fail_response(500, str(e))


@ns.route('/getUserList')
class UserList(Resource):
    parser = reqparse.RequestParser()
    parser.add_argument('limit', type=int, default=10, help='Number of users to retrieve')
    parser.add_argument('page', type=int, default=1, help='Page number')
    parser.add_argument('key', type=str, help='Search key')
    parser.add_argument('code', type=str, help='Code filter')
    @ns.expect(parser)
    def get(self):
        """获取用户列表"""
        args = self.parser.parse_args()
        logger.info(f"Pagination params: page={args.page}, limit={args.limit}")
        data =  UserService.get_user_list(**args)
        data['users'] = [{'username': user['username'], 'nickname': user['nickname'], 'enterpriseName': user['com_name'], 'departmentName': user['dep_name']} for user in data['items']]
        return success_response(data)

