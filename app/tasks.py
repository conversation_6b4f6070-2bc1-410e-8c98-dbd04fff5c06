# tasks.py

import logging
import random
from flask_apscheduler import APScheduler
# from apscheduler.schedulers.background import BackgroundScheduler
from app.service.sync_data import SyncDataService
from app.models.task_log import TaskLog  # 直接导入 TaskLog
from datetime import datetime
from app.extension import db
logger = logging.getLogger("flask_app")
logger.info("Scheduler started")

def start_pull_task(app):
    """增量同步数据"""
    logger.info("=============== Start Pull Task ===============")
    token_id = ''
    # token_id = '4e4f0980-c208-4994-8bbc-46c148015fe1'
    sync_service = SyncDataService(token_id=token_id)
    
    try:
        # 执行增量同步任务，所有的日志记录和错误处理都在service中实现
        sync_service.pull_task(app)
        # 确保所有数据库操作都已提交
        with app.app_context():
            db.session.commit()
    except Exception as e:
        logger.error(f"执行增量同步任务时发生异常: {e}", exc_info=True)
        # 出现异常时，也要确保所有操作都被提交
        try:
            with app.app_context():
                db.session.commit()
        except Exception:
            pass
    finally:
        # 登出前确保所有数据库变更都已提交
        try:
            with app.app_context():
                db.session.commit()
        except Exception:
            pass
        # 尝试登出
        sync_service.logout()
        logger.info("=============== Finished Pull Task ===============")


def start_sync_task(app):
    """全量同步数据"""
    logger.info("=============== Start Sync Task ===============")
    token_id = ''
    # token_id = '4e4f0980-c208-4994-8bbc-46c148015fe1'
    sync_service = SyncDataService(token_id=token_id)
    
    try:
        # 执行全量同步任务，所有的日志记录和错误处理都在service中实现
        sync_service.sync_task(app)
        # 确保所有数据库操作都已提交
        with app.app_context():
            db.session.commit()
    except Exception as e:
        logger.error(f"执行全量同步任务时发生异常: {e}", exc_info=True)
        # 出现异常时，也要确保所有操作都被提交
        try:
            with app.app_context():
                db.session.commit()
        except Exception:
            pass
    finally:
        # 登出前确保所有数据库变更都已提交
        try:
            with app.app_context():
                db.session.commit()
        except Exception:
            pass
        # 尝试登出
        sync_service.logout()
        logger.info("=============== Finished Sync Task ===============")


def setup_schedule(app):
    logger.info("Setting up scheduler")
    scheduler = APScheduler()
    scheduler.init_app(app)
    scheduler.start()
    
    # 增量任务每天凌晨2,3,4点随机一个时间点执行，分钟为0-30的随机数（除了28号）
    pull_hour = random.choice([2, 3, 4])
    pull_minute = random.randint(0, 30)
    logger.info(f"设置增量同步任务时间为 {pull_hour}:{pull_minute}")
    
    scheduler.add_job(
        id='pull_task', 
        func=start_pull_task, 
        args=[scheduler.app], 
        trigger='cron', 
        hour=pull_hour, 
        minute=pull_minute, 
        day='1-27,29-31'
    )
    
    # 全量任务每月28号凌晨1或2点随机一个时间点执行，分钟为0-30的随机数
    sync_hour = random.choice([1, 2])
    sync_minute = random.randint(0, 30)
    logger.info(f"设置全量同步任务时间为 {sync_hour}:{sync_minute}")
    
    scheduler.add_job(
        id='sync_task', 
        func=start_sync_task, 
        args=[scheduler.app], 
        trigger='cron', 
        hour=sync_hour, 
        minute=sync_minute, 
        day='28'
    )
    
    # 测试用的调度，取消注释可立即执行一次
    # scheduler.add_job(id='sync_task', func=start_sync_task, args=[scheduler.app], trigger='date', run_date=datetime.now())
